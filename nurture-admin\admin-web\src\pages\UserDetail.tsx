import { useParams, useNavigate } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, Button, Badge } from '../components/ui'
import { adminApi } from '../lib/api'

export function UserDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (id) {
      fetchUserDetails()
    }
  }, [id])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await adminApi.getUserById(id!)
      
      if (response.success && response.data) {
        setUser(response.data.user)
      } else {
        setError('Failed to fetch user details')
      }
    } catch (err: any) {
      console.error('Error fetching user details:', err)
      setError(err.response?.data?.message || 'Failed to fetch user details')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = async () => {
    if (!user) return

    try {
      const newStatus = !user.isActive
      const response = await adminApi.updateUserStatus(user.id, newStatus)

      if (response.success) {
        setUser({ ...user, isActive: newStatus })
      } else {
        setError('Failed to update user status')
      }
    } catch (err: any) {
      console.error('Error updating user status:', err)
      setError(err.response?.data?.message || 'Failed to update user status')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading user details...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/admin/users')}>
            ← Back to Users
          </Button>
        </div>
        <Card variant="elevated">
          <CardContent className="p-12 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchUserDetails}>Try Again</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="space-y-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/admin/users')}>
            ← Back to Users
          </Button>
        </div>
        <Card variant="elevated">
          <CardContent className="p-12 text-center">
            <p className="text-gray-500">User not found</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button variant="ghost" onClick={() => navigate('/admin/users')}>
            ← Back to Users
          </Button>
          <div>
            <h1 className="text-xl font-bold text-gray-900">
              {user.firstName || 'Unknown'} {user.lastName || 'User'}
            </h1>
            <p className="text-sm text-gray-600 mt-1">User Details & Management</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={user.isActive ? 'success' : 'error'}>
            {user.isActive ? 'Active' : 'Inactive'}
          </Badge>
          <Badge variant={user.emailVerified ? 'success' : 'warning'}>
            {user.emailVerified ? 'Verified' : 'Unverified'}
          </Badge>
          <Button
            variant={user.isActive ? 'danger' : 'primary'}
            onClick={handleToggleStatus}
          >
            {user.isActive ? 'Deactivate User' : 'Activate User'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-4">
          {/* Basic Information */}
          <Card variant="elevated">
            <CardHeader>
              <h3 className="text-sm font-semibold">Basic Information</h3>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <p className="text-gray-900">{user.firstName || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                  <p className="text-gray-900">{user.lastName || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <p className="text-gray-900">{user.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <p className="text-gray-900">{user.phone || 'Not provided'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                  <p className="text-gray-900">
                    {user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString() : 'Not provided'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                  <p className="text-gray-900 capitalize">{user.gender || 'Not provided'}</p>
                </div>

              </div>
            </CardContent>
          </Card>

          {/* Account Information */}
          <Card variant="elevated">
            <CardHeader>
              <h3 className="text-lg font-semibold">Account Information</h3>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Account Status</label>
                  <Badge variant={user.isActive ? 'success' : 'error'}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email Verification</label>
                  <Badge variant={user.emailVerified ? 'success' : 'warning'}>
                    {user.emailVerified ? 'Verified' : 'Unverified'}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                  <p className="text-gray-900">{new Date(user.createdAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Login</label>
                  <p className="text-gray-900">Not available</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile Picture */}
          <Card variant="elevated">
            <CardHeader>
              <h3 className="text-lg font-semibold">Profile Picture</h3>
            </CardHeader>
            <CardContent className="text-center">
              <div className="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-primary font-semibold text-2xl">
                  {(user.firstName || 'U').charAt(0)}{(user.lastName || 'U').charAt(0)}
                </span>
              </div>
              <p className="text-sm text-gray-600">
                {user.profilePictureUrl ? 'Custom profile picture' : 'Default avatar'}
              </p>
            </CardContent>
          </Card>

          {/* Appointment Statistics */}
          {user.appointmentStats && (
            <Card variant="elevated">
              <CardHeader>
                <h3 className="text-lg font-semibold">Appointment Statistics</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Appointments:</span>
                    <span className="font-semibold">{user.appointmentStats.total_appointments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Completed:</span>
                    <span className="font-semibold text-green-600">{user.appointmentStats.completed_appointments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cancelled:</span>
                    <span className="font-semibold text-red-600">{user.appointmentStats.cancelled_appointments || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pending:</span>
                    <span className="font-semibold text-yellow-600">{user.appointmentStats.pending_appointments || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
