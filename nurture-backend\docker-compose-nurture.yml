version: '3.8'

services:

  # Node.js Backend Application
  backend:
    image: 'cloudstier/nurture-backend:v1'
    container_name: nurture-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${PORT:-3000}
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME:-nurture_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-Cloud@2025}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-24h}
    ports:
      - "${PORT:-5374}:3000"
    networks:
      - postgresnetwork

    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      


networks:
  postgresnetwork:
    external: true