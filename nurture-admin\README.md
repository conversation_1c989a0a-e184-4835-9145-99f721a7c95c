# Nurture Wellness Admin Portal

A comprehensive React web application for managing the Nurture Wellness platform, built with TypeScript, Tailwind CSS, and modern web technologies.

## 🌟 Features

### 🔐 Authentication & Security
- **Secure Admin Login**: JWT-based authentication with session management
- **Role-Based Access Control**: Super Admin, Admin, and Moderator roles
- **Protected Routes**: Automatic redirection for unauthorized access
- **Activity Logging**: Comprehensive audit trail for all admin actions

### 🏥 Institute Management
- **Application Review**: View and manage institute registration applications
- **Approval Workflow**: Approve, reject, or request more information
- **Status Tracking**: Real-time status updates and notifications
- **Detailed Views**: Complete institute profiles with owner information

### 👥 User Management
- **User Overview**: View all registered users with filtering options
- **Account Control**: Activate/deactivate user accounts
- **User Analytics**: Track user engagement and activity

### 📊 Dashboard & Analytics
- **Real-time Statistics**: Institute and user metrics
- **Activity Feed**: Recent platform activities and changes
- **Quick Actions**: Fast access to common administrative tasks

### 🎨 Design System
- **Consistent UI**: Matches the mobile app's Cameroon-themed design
- **Responsive Design**: Optimized for desktop and tablet usage
- **Accessibility**: WCAG compliant interface elements

## 🛠 Tech Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and building
- **Styling**: Tailwind CSS with custom design tokens
- **State Management**: Zustand for lightweight state management
- **Routing**: React Router v6 with protected routes
- **HTTP Client**: Axios with interceptors for API communication
- **Icons**: Lucide React for consistent iconography
- **UI Components**: Custom component library inspired by shadcn/ui

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Running Nurture Backend API

### Installation

1. **Navigate to the admin web directory:**
   ```bash
   cd nurture-admin/admin-web
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```

   Update `.env` with your API configuration:
   ```env
   VITE_API_URL=http://localhost:3000/api
   VITE_APP_NAME=Nurture Admin
   VITE_APP_VERSION=1.0.0
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🔑 Default Admin Credentials

For initial setup and testing:

```
Email: <EMAIL>
Password: admin123
Role: Super Admin
```

```
Email: <EMAIL>
Password: admin123
Role: Moderator
```

## 📱 Pages & Features

### 🏠 Landing Page
- Overview of the admin system
- Feature highlights
- Platform statistics
- Quick access to login

### 🔐 Login Page
- Secure authentication form
- Form validation and error handling
- Remember me functionality
- Demo credentials display

### 📊 Dashboard
- Key metrics and statistics
- Recent activity feed
- Quick action buttons
- System health indicators

### 🏥 Institutes Management
- **List View**: Paginated institute listings with filters
- **Detail View**: Complete institute information and documents
- **Status Management**: Approve, reject, or update institute status
- **Search & Filter**: Find institutes by name, status, or location

### 👥 Users Management
- **User Directory**: All registered users with search functionality
- **Account Control**: Activate/deactivate user accounts
- **User Analytics**: Registration trends and activity metrics
- **Bulk Actions**: Manage multiple users simultaneously

## 🎨 Design System

### Color Palette (Cameroon Theme)
```css
Primary: #007A5E (Cameroon Green)
Secondary: #FCD116 (Cameroon Yellow)
Accent: #CE1126 (Cameroon Red)
Background: #FFFFFF
Surface: #F5F5F5
```

### Typography
- **Font Family**: Inter (Google Fonts)
- **Scale**: 12px to 36px with consistent line heights
- **Weights**: 300, 400, 500, 600, 700

### Components
- **Buttons**: Multiple variants (primary, secondary, outline, ghost, danger)
- **Inputs**: Form controls with validation states
- **Cards**: Content containers with elevation options
- **Badges**: Status indicators with semantic colors
- **Loading States**: Spinners and skeleton loaders

## 🔧 Development

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components
│   ├── Layout.tsx      # Main layout wrapper
│   └── ProtectedRoute.tsx
├── pages/              # Page components
│   ├── Landing.tsx
│   ├── Login.tsx
│   ├── Dashboard.tsx
│   ├── Institutes.tsx
│   ├── InstituteDetail.tsx
│   └── Users.tsx
├── stores/             # Zustand stores
│   └── authStore.ts
├── lib/                # Utilities and configurations
│   ├── api.ts          # API client and endpoints
│   └── utils.ts        # Helper functions
├── types/              # TypeScript type definitions
│   └── index.ts
└── App.tsx             # Main application component
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### API Integration

The admin portal integrates with the Nurture Backend API:

```typescript
// Authentication
POST /api/admin/auth/login
GET /api/admin/auth/profile
POST /api/admin/auth/logout

// Dashboard
GET /api/admin/dashboard

// Institutes
GET /api/admin/institutes
GET /api/admin/institutes/:id
PATCH /api/admin/institutes/:id/status

// Users
GET /api/admin/users
PATCH /api/admin/users/:id/status

// Admin Management
GET /api/admin/admins
POST /api/admin/admins
PUT /api/admin/admins/:id
DELETE /api/admin/admins/:id
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Route Protection**: Automatic redirection for unauthorized access
- **Role-Based Permissions**: Different access levels for different admin roles
- **Activity Logging**: Complete audit trail of admin actions
- **Session Management**: Automatic token refresh and logout
- **Input Validation**: Client and server-side validation

## 🚀 Deployment

### Environment Setup
1. Set up environment variables for production
2. Configure API endpoints
3. Build the application
4. Deploy to your preferred hosting platform

### Recommended Hosting
- **Vercel**: Zero-config deployment with automatic HTTPS
- **Netlify**: Easy deployment with form handling
- **AWS S3 + CloudFront**: Scalable static hosting
- **Docker**: Containerized deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is part of the Nurture Wellness platform.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the API documentation
- Contact the development team

---

**Built with ❤️ for the Nurture Wellness Platform**