import React, { useState, useEffect } from 'react'
import { adminApi } from '../lib/api'
import { Card, CardContent } from '../components/ui/Card'
import EndpointDetailsModal from '../components/EndpointDetailsModal'

interface EndpointData {
  endpoint: string
  method: string
  total_calls: number
  success_calls: number
  client_errors: number
  server_errors: number
  avg_response_time: number
  min_response_time: number
  max_response_time: number
  user_calls: number
  admin_calls: number
  institute_calls: number
  anonymous_calls: number
  unique_ips: number
  first_call: string
  last_call: string
  today_calls: number
  yesterday_calls: number
  today_success: number
  yesterday_success: number
  success_rate: number
  error_rate: number
  today_vs_yesterday: {
    calls_change: number
    success_change: number
  }
}

interface ComprehensiveData {
  endpoints: EndpointData[]
  summary: {
    total_calls: number
    total_endpoints: number
    avg_response_time: number
    timeframe: string
  }
}

const ComprehensiveAnalytics: React.FC = () => {
  const [data, setData] = useState<ComprehensiveData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeframe, setTimeframe] = useState('24h')
  const [selectedEndpoint, setSelectedEndpoint] = useState<EndpointData | null>(null)
  const [sortBy, setSortBy] = useState<'total_calls' | 'today_calls' | 'success_rate' | 'avg_response_time'>('total_calls')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filterCategory, setFilterCategory] = useState<string>('all')

  const fetchComprehensiveData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await adminApi.getComprehensiveAnalytics(timeframe)
      
      if (response.success) {
        setData(response.data)
      } else {
        setError(response.message || 'Failed to fetch analytics data')
      }
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch analytics data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchComprehensiveData()
  }, [timeframe])

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return '↗️'
    if (change < 0) return '↘️'
    return '➡️'
  }

  const getCategoryFromEndpoint = (endpoint: string) => {
    if (endpoint.includes('/auth')) return 'Authentication'
    if (endpoint.includes('/institutes')) return 'Institutes'
    if (endpoint.includes('/appointments')) return 'Appointments'
    if (endpoint.includes('/users')) return 'Users'
    if (endpoint.includes('/staff')) return 'Staff'
    if (endpoint.includes('/services')) return 'Services'
    if (endpoint.includes('/admin')) return 'Admin'
    if (endpoint.includes('/location')) return 'Location'
    return 'Other'
  }

  const filteredAndSortedEndpoints = data?.endpoints
    .filter(endpoint => {
      if (filterCategory === 'all') return true
      return getCategoryFromEndpoint(endpoint.endpoint) === filterCategory
    })
    .sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      
      if (sortOrder === 'desc') {
        return bValue - aValue
      } else {
        return aValue - bValue
      }
    }) || []

  const categories = ['all', ...new Set(data?.endpoints.map(ep => getCategoryFromEndpoint(ep.endpoint)) || [])]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading API analytics...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card variant="elevated">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p className="text-lg font-semibold">Error Loading Analytics</p>
            <p className="mt-2">{error}</p>
            <button
              onClick={fetchComprehensiveData}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">API Endpoint Search & Details</h2>
          <p className="mt-1 text-sm text-gray-600">
            Detailed view of all API endpoints with yesterday vs today comparisons
          </p>
        </div>

        {/* Timeframe Selector */}
        <div className="mt-4 sm:mt-0">
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* Summary Stats */}
      {data?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card variant="elevated">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Total API Calls</p>
                  <p className="text-lg font-bold text-gray-900 mt-1">{formatNumber(data.summary.total_calls)}</p>
                </div>
                <div className="text-2xl">📊</div>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Total Endpoints</p>
                  <p className="text-lg font-bold text-gray-900 mt-1">{formatNumber(data.summary.total_endpoints)}</p>
                </div>
                <div className="text-2xl">🔗</div>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Avg Response Time</p>
                  <p className="text-lg font-bold text-gray-900 mt-1">{data.summary.avg_response_time}ms</p>
                </div>
                <div className="text-2xl">⚡</div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Sorting */}
      <Card variant="elevated">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="total_calls">Total Calls</option>
                  <option value="today_calls">Today's Calls</option>
                  <option value="success_rate">Success Rate</option>
                  <option value="avg_response_time">Response Time</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Order</label>
                <select
                  value={sortOrder}
                  onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="desc">Descending</option>
                  <option value="asc">Ascending</option>
                </select>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              Showing {filteredAndSortedEndpoints.length} of {data?.endpoints.length || 0} endpoints
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Endpoints Table */}
      <Card variant="elevated">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Endpoint
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Calls
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Today vs Yesterday
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Response
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User Types
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAndSortedEndpoints.map((endpoint) => (
                  <tr key={`${endpoint.endpoint}-${endpoint.method}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900 max-w-xs truncate" title={endpoint.endpoint}>
                            {endpoint.endpoint}
                          </div>
                          <div className="text-xs text-gray-500">
                            {getCategoryFromEndpoint(endpoint.endpoint)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                        endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                        endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {endpoint.method}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatNumber(endpoint.total_calls)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatNumber(endpoint.success_calls)} success
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-medium text-gray-900">
                            {formatNumber(endpoint.today_calls)}
                          </span>
                          <span className="text-xs text-gray-500">today</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className="text-sm text-gray-600">
                            {formatNumber(endpoint.yesterday_calls)}
                          </span>
                          <span className="text-xs text-gray-500">yesterday</span>
                        </div>
                        <div className={`flex items-center space-x-1 ${getChangeColor(endpoint.today_vs_yesterday.calls_change)}`}>
                          <span className="text-xs">
                            {getChangeIcon(endpoint.today_vs_yesterday.calls_change)}
                            {Math.abs(endpoint.today_vs_yesterday.calls_change)}%
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className={`text-sm font-medium ${
                            endpoint.success_rate >= 95 ? 'text-green-600' :
                            endpoint.success_rate >= 90 ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {endpoint.success_rate}%
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                            <div
                              className={`h-1.5 rounded-full ${
                                endpoint.success_rate >= 95 ? 'bg-green-500' :
                                endpoint.success_rate >= 90 ? 'bg-yellow-500' :
                                'bg-red-500'
                              }`}
                              style={{ width: `${endpoint.success_rate}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {endpoint.avg_response_time}ms
                      </div>
                      <div className="text-xs text-gray-500">
                        {endpoint.min_response_time}-{endpoint.max_response_time}ms
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {endpoint.user_calls > 0 && (
                          <span className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                            U:{formatNumber(endpoint.user_calls)}
                          </span>
                        )}
                        {endpoint.admin_calls > 0 && (
                          <span className="inline-flex px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                            A:{formatNumber(endpoint.admin_calls)}
                          </span>
                        )}
                        {endpoint.institute_calls > 0 && (
                          <span className="inline-flex px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                            I:{formatNumber(endpoint.institute_calls)}
                          </span>
                        )}
                        {endpoint.anonymous_calls > 0 && (
                          <span className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                            An:{formatNumber(endpoint.anonymous_calls)}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => setSelectedEndpoint(endpoint)}
                        className="text-blue-600 hover:text-blue-900 font-medium"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Endpoint Details Modal */}
      {selectedEndpoint && (
        <EndpointDetailsModal
          endpoint={selectedEndpoint.endpoint}
          method={selectedEndpoint.method}
          isOpen={!!selectedEndpoint}
          onClose={() => setSelectedEndpoint(null)}
        />
      )}
    </div>
  )
}

export default ComprehensiveAnalytics
