import React, { useState, useEffect } from 'react'
import { adminApi } from '../lib/api'
import { Card, CardContent } from './ui/Card'

interface EndpointCall {
  id: number
  method: string
  url: string
  endpoint: string
  status_code: number
  response_time: number
  response_size: number
  user_type: string
  user_id: number | null
  admin_id: number | null
  ip_address: string
  user_agent: string
  error_message: string | null
  created_at: string
  metadata: any
}

interface EndpointDetailsData {
  calls: EndpointCall[]
  pagination: {
    total: number
    limit: number
    offset: number
    has_more: boolean
  }
  endpoint: string
  method?: string
  timeframe: string
  date?: string
}

interface EndpointDetailsModalProps {
  endpoint: string
  method?: string
  isOpen: boolean
  onClose: () => void
}

const EndpointDetailsModal: React.FC<EndpointDetailsModalProps> = ({
  endpoint,
  method,
  isOpen,
  onClose
}) => {
  const [data, setData] = useState<EndpointDetailsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [timeframe, setTimeframe] = useState('24h')
  const [selectedDate, setSelectedDate] = useState('')
  const [currentPage, setCurrentPage] = useState(0)
  const [limit] = useState(50)

  const fetchEndpointDetails = async () => {
    if (!endpoint) return

    try {
      setLoading(true)
      setError(null)

      const params = {
        timeframe: selectedDate ? undefined : timeframe,
        date: selectedDate || undefined,
        limit,
        offset: currentPage * limit
      }

      const response = await adminApi.getEndpointCallDetails(endpoint, method, params)
      
      if (response.success) {
        setData(response.data)
      } else {
        setError(response.message || 'Failed to fetch endpoint details')
      }
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch endpoint details')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen && endpoint) {
      setCurrentPage(0)
      fetchEndpointDetails()
    }
  }, [isOpen, endpoint, method, timeframe, selectedDate])

  useEffect(() => {
    if (isOpen && endpoint) {
      fetchEndpointDetails()
    }
  }, [currentPage])

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  const getStatusColor = (statusCode: number) => {
    if (statusCode >= 200 && statusCode < 300) return 'text-green-600 bg-green-100'
    if (statusCode >= 300 && statusCode < 400) return 'text-yellow-600 bg-yellow-100'
    if (statusCode >= 400 && statusCode < 500) return 'text-orange-600 bg-orange-100'
    if (statusCode >= 500) return 'text-red-600 bg-red-100'
    return 'text-gray-600 bg-gray-100'
  }

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'user': return 'text-blue-600 bg-blue-100'
      case 'admin': return 'text-purple-600 bg-purple-100'
      case 'institute': return 'text-green-600 bg-green-100'
      case 'anonymous': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getYesterdayDate = () => {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    return yesterday.toISOString().split('T')[0]
  }

  const getTodayDate = () => {
    return new Date().toISOString().split('T')[0]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">API Call Details</h2>
              <p className="text-sm text-gray-600 mt-1">
                {endpoint} {method && `(${method})`}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center gap-4 mt-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setSelectedDate(getTodayDate())
                  setCurrentPage(0)
                }}
                className={`px-3 py-1 text-sm rounded ${
                  selectedDate === getTodayDate() 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Today
              </button>
              <button
                onClick={() => {
                  setSelectedDate(getYesterdayDate())
                  setCurrentPage(0)
                }}
                className={`px-3 py-1 text-sm rounded ${
                  selectedDate === getYesterdayDate() 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Yesterday
              </button>
              <button
                onClick={() => {
                  setSelectedDate('')
                  setCurrentPage(0)
                }}
                className={`px-3 py-1 text-sm rounded ${
                  !selectedDate 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                All Time
              </button>
            </div>

            {!selectedDate && (
              <select
                value={timeframe}
                onChange={(e) => {
                  setTimeframe(e.target.value)
                  setCurrentPage(0)
                }}
                className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1h">Last Hour</option>
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
              </select>
            )}

            <input
              type="date"
              value={selectedDate}
              onChange={(e) => {
                setSelectedDate(e.target.value)
                setCurrentPage(0)
              }}
              className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading details...</p>
              </div>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-600">
              <p className="font-semibold">Error Loading Details</p>
              <p className="mt-1">{error}</p>
              <button 
                onClick={fetchEndpointDetails}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          ) : data ? (
            <div className="p-6">
              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <Card variant="elevated">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">{formatNumber(data.pagination.total)}</p>
                      <p className="text-sm text-gray-600">Total Calls</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card variant="elevated">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        {data.calls.filter(call => call.status_code >= 200 && call.status_code < 300).length}
                      </p>
                      <p className="text-sm text-gray-600">Success</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card variant="elevated">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-red-600">
                        {data.calls.filter(call => call.status_code >= 400).length}
                      </p>
                      <p className="text-sm text-gray-600">Errors</p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card variant="elevated">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {data.calls.length > 0 ? Math.round(data.calls.reduce((sum, call) => sum + call.response_time, 0) / data.calls.length) : 0}ms
                      </p>
                      <p className="text-sm text-gray-600">Avg Response</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Calls Table */}
              <Card variant="elevated">
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Response Time
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User Type
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            IP Address
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            URL
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Error
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {data.calls.map((call) => (
                          <tr key={call.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                              {formatDateTime(call.created_at)}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(call.status_code)}`}>
                                {call.status_code}
                              </span>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                              {call.response_time}ms
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserTypeColor(call.user_type)}`}>
                                {call.user_type}
                              </span>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                              {call.ip_address}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 max-w-xs truncate" title={call.url}>
                              {call.url}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-red-600 max-w-xs truncate" title={call.error_message || ''}>
                              {call.error_message || '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Pagination */}
              {data.pagination.total > limit && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-600">
                    Showing {currentPage * limit + 1} to {Math.min((currentPage + 1) * limit, data.pagination.total)} of {formatNumber(data.pagination.total)} calls
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                      disabled={currentPage === 0}
                      className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Previous
                    </button>
                    <span className="px-3 py-1 text-sm text-gray-600">
                      Page {currentPage + 1} of {Math.ceil(data.pagination.total / limit)}
                    </span>
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={!data.pagination.has_more}
                      className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}

export default EndpointDetailsModal
