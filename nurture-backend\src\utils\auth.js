const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
const hashPassword = async (password) => {
  try {
    // Reduced from 12 to 10 for better performance while maintaining security
    // 10 rounds is still very secure and significantly faster than 12 rounds
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 10;
    return await bcrypt.hash(password, saltRounds);
  } catch (error) {
    throw new Error('Error hashing password');
  }
};

/**
 * Compare a plain text password with a hashed password
 * @param {string} password - Plain text password
 * @param {string} hashedPassword - Hashed password from database
 * @returns {Promise<boolean>} - True if passwords match
 */
const comparePassword = async (password, hashedPassword) => {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    throw new Error('Error comparing passwords');
  }
};

/**
 * Generate a JWT token
 * @param {object} payload - Data to include in token
 * @param {boolean} rememberMe - Whether to use extended expiration
 * @returns {object} - Object containing token and expiration info
 */
const generateToken = (payload, rememberMe = false) => {
  try {
    const secret = process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    // Set expiration based on remember me option
    const expiresIn = rememberMe
      ? process.env.JWT_EXPIRES_IN_REMEMBER || '30d'  // 30 days for remember me
      : process.env.JWT_EXPIRES_IN || '1d';           // 1 day for regular login

    const token = jwt.sign(payload, secret, { expiresIn });

    // Calculate expiration timestamp
    const expirationMs = rememberMe
      ? 30 * 24 * 60 * 60 * 1000  // 30 days in milliseconds
      : 24 * 60 * 60 * 1000;      // 1 day in milliseconds

    const expiresAt = new Date(Date.now() + expirationMs).toISOString();

    return {
      token,
      expiresAt
    };
  } catch (error) {
    throw new Error('Error generating token');
  }
};

/**
 * Verify a JWT token
 * @param {string} token - JWT token to verify
 * @returns {object} - Decoded token payload
 */
const verifyToken = (token) => {
  try {
    const secret = process.env.JWT_SECRET;
    
    if (!secret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }
    
    return jwt.verify(token, secret);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token has expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else {
      throw new Error('Error verifying token');
    }
  }
};

/**
 * Extract token from Authorization header
 * @param {string} authHeader - Authorization header value
 * @returns {string|null} - Extracted token or null
 */
const extractTokenFromHeader = (authHeader) => {
  if (!authHeader) {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};

module.exports = {
  hashPassword,
  comparePassword,
  generateToken,
  verifyToken,
  extractTokenFromHeader
};
