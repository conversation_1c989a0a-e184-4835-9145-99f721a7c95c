-- =====================================================
-- ADMIN USERS SEED DATA
-- =====================================================
-- This script creates default admin users for the Nurture application
-- Passwords are hashed using bcrypt with salt rounds 12
-- Default password for all admins: admin123
-- =====================================================

-- Insert Super Admin
INSERT INTO admins (
    email, 
    password, 
    first_name, 
    last_name, 
    phone,
    role, 
    permissions,
    email_verified,
    is_active,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2b$12$EpruGEBOsnr.kJJLqJZZwOs.grJm3QSAKIPtK3tq2WhiNT.jby36m', -- admin123
    'Super',
    'Admin',
    '+237123456789',
    'super_admin',
    '["all"]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    password = EXCLUDED.password,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    permissions = EXCLUDED.permissions,
    email_verified = EXCLUDED.email_verified,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Admin Moderator
INSERT INTO admins (
    email, 
    password, 
    first_name, 
    last_name, 
    phone,
    role, 
    permissions,
    email_verified,
    is_active,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2b$12$EpruGEBOsnr.kJJLqJZZwOs.grJm3QSAKIPtK3tq2WhiNT.jby36m', -- admin123
    'Admin',
    'Moderator',
    '+237123456790',
    'moderator',
    '["institutes:read", "institutes:update", "users:read", "services:read", "appointments:read"]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    password = EXCLUDED.password,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    permissions = EXCLUDED.permissions,
    email_verified = EXCLUDED.email_verified,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Content Admin
INSERT INTO admins (
    email, 
    password, 
    first_name, 
    last_name, 
    phone,
    role, 
    permissions,
    email_verified,
    is_active,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2b$12$EpruGEBOsnr.kJJLqJZZwOs.grJm3QSAKIPtK3tq2WhiNT.jby36m', -- admin123
    'Content',
    'Manager',
    '+237123456791',
    'admin',
    '["services:all", "institutes:read", "institutes:update", "content:all"]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO UPDATE SET
    password = EXCLUDED.password,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    permissions = EXCLUDED.permissions,
    email_verified = EXCLUDED.email_verified,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- ADMIN SEED DATA COMPLETION
-- =====================================================
-- Total admins created: 3
-- - Super Admin (<EMAIL>) - Full access
-- - Moderator (<EMAIL>) - Limited access
-- - Content Manager (<EMAIL>) - Content management
-- Default password: admin123
-- =====================================================
