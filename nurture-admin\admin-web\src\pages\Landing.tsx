import { Link } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import { <PERSON><PERSON>, Card, CardContent } from '../components/ui'

export function Landing() {
  const { isAuthenticated } = useAuthStore()

  const features = [
    {
      icon: '🏥',
      title: 'Institute Management',
      description: 'Review, approve, and manage wellness institute applications with comprehensive oversight.',
    },
    {
      icon: '✅',
      title: 'Approval Workflows',
      description: 'Streamlined approval processes with status tracking and automated notifications.',
    },
    {
      icon: '👥',
      title: 'User Administration',
      description: 'Manage user accounts, permissions, and access controls across the platform.',
    },
    {
      icon: '📊',
      title: 'Analytics Dashboard',
      description: 'Real-time insights and reporting on platform usage and institute performance.',
    },
    {
      icon: '🔒',
      title: 'Security & Compliance',
      description: 'Enterprise-grade security with audit trails and compliance monitoring.',
    },
    {
      icon: '🌍',
      title: 'Multi-location Support',
      description: 'Manage institutes across multiple cities and regions in Cameroon.',
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Header */}
      <header className="glass-effect shadow-xl border-b border-white/20">
        <div className="max-w-5xl mx-auto px-5 lg:px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-base">N</span>
              </div>
              <div className="flex flex-col">
                <span className="text-base font-bold text-gray-900 tracking-tight">Nurture Admin</span>
                <span className="text-xs text-gray-500 font-medium">Management Portal</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {isAuthenticated ? (
                <Link to="/admin/dashboard">
                  <Button size="lg" className="shadow-xl">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Go to Dashboard
                  </Button>
                </Link>
              ) : (
                <Link to="/login">
                  <Button size="lg" className="shadow-xl">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m0 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Admin Login
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5"></div>
        <div className="relative max-w-7xl mx-auto px-6 lg:px-8 text-center">
          <div className="animate-slide-in">
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 tracking-tight">
              Nurture Wellness
              <span className="block bg-gradient-to-r from-primary to-primary-600 bg-clip-text text-transparent">
                Admin Portal
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Comprehensive administrative platform for managing wellness institutes,
              user accounts, and approval workflows across Cameroon's healthcare ecosystem.
            </p>
            {!isAuthenticated && (
              <Link to="/login">
                <Button size="lg" className="px-12 py-4 text-lg shadow-2xl">
                  <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Access Admin Portal
                </Button>
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
              Powerful Admin Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Everything you need to manage the Nurture Wellness platform effectively with professional-grade tools
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} variant="elevated" className="h-full group hover:scale-105 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-24 bg-gradient-to-r from-primary via-primary-600 to-primary-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 text-center">
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-4 group-hover:scale-110 transition-transform duration-300">50+</div>
              <div className="text-primary-100 text-lg font-medium">Registered Institutes</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-4 group-hover:scale-110 transition-transform duration-300">1000+</div>
              <div className="text-primary-100 text-lg font-medium">Active Users</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-4 group-hover:scale-110 transition-transform duration-300">10</div>
              <div className="text-primary-100 text-lg font-medium">Cities Covered</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-4 group-hover:scale-110 transition-transform duration-300">24/7</div>
              <div className="text-primary-100 text-lg font-medium">System Monitoring</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center space-y-4">
            <div className="flex items-center space-x-6">
              <Link
                to="/privacy-policy"
                className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group"
              >
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span className="font-medium">Privacy Policy & Terms</span>
              </Link>
            </div>
            <p className="text-gray-400">&copy; 2024 Nurture Wellness. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
