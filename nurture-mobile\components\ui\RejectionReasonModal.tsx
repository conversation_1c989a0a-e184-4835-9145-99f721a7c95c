import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '@/lib/i18n';

interface RejectionReasonModalProps {
  visible: boolean;
  onClose: () => void;
  rejectionReason: string;
  instituteName: string;
}

export function RejectionReasonModal({
  visible,
  onClose,
  rejectionReason,
  instituteName,
}: RejectionReasonModalProps) {
  const { t } = useTranslation();

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1">
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
            <View className="flex-1">
              <Text className="text-lg font-semibold text-gray-900 text-center">
                {t('institute.rejectionReason')}
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} className="absolute right-4">
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <ScrollView className="flex-1 p-6">
            <View className="mb-6">
              <Text className="text-base font-medium text-gray-900 mb-2">
                {t('institute.instituteName')}
              </Text>
              <Text className="text-gray-700 text-base">
                {instituteName}
              </Text>
            </View>

            <View className="mb-6">
              <Text className="text-base font-medium text-gray-900 mb-2">
                {t('institute.status')}
              </Text>
              <View className="flex-row items-center">
                <View className="px-3 py-1 rounded-full bg-red-100">
                  <Text className="text-red-600 text-sm font-medium">
                    {t('institute.rejected')}
                  </Text>
                </View>
              </View>
            </View>

            <View className="mb-6">
              <Text className="text-base font-medium text-gray-900 mb-3">
                {t('institute.reasonForRejection')}
              </Text>
              <View className="bg-red-50 border border-red-200 rounded-lg p-4">
                <Text className="text-gray-800 text-base leading-6">
                  {rejectionReason && rejectionReason.trim() ? rejectionReason : t('institute.noReasonProvided')}
                </Text>
              </View>
            </View>

            <View className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <View className="flex-row items-start">
                <Ionicons name="information-circle" size={20} color="#3B82F6" className="mr-2 mt-0.5" />
                <View className="flex-1 ml-2">
                  <Text className="text-blue-800 text-sm font-medium mb-1">
                    {t('institute.nextSteps')}
                  </Text>
                  <Text className="text-blue-700 text-sm leading-5">
                    {t('institute.rejectionNextStepsMessage')}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Footer */}
          <View className="p-6 border-t border-gray-200">
            <TouchableOpacity
              onPress={onClose}
              className="bg-primary rounded-lg py-3 px-6"
            >
              <Text className="text-white text-center font-medium text-base">
                {t('common.understood')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
}
