/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#007A5E',
          50: '#E6F7F3',
          100: '#CCEFE7',
          200: '#99DFCF',
          300: '#66CFB7',
          400: '#33BF9F',
          500: '#007A5E',
          600: '#006B52',
          700: '#005C46',
          800: '#004D3A',
          900: '#003E2E',
        },
        secondary: {
          DEFAULT: '#FCD116',
          50: '#FFFDF0',
          100: '#FFFBE0',
          200: '#FFF7C2',
          300: '#FFF3A3',
          400: '#FFEF85',
          500: '#FCD116',
          600: '#E6BC14',
          700: '#D1A712',
          800: '#BC9210',
          900: '#A77D0E',
        },
        accent: {
          DEFAULT: '#CE1126',
          50: '#FCE8EA',
          100: '#F9D1D5',
          200: '#F3A3AB',
          300: '#ED7581',
          400: '#E74757',
          500: '#CE1126',
          600: '#B50E21',
          700: '#9C0C1C',
          800: '#830A17',
          900: '#6A0812',
        },
        background: '#FFFFFF',
        surface: '#F5F5F5',
        text: {
          DEFAULT: '#1F2937',
          secondary: '#6B7280',
          muted: '#9CA3AF',
        },
        border: {
          DEFAULT: '#E5E7EB',
          light: '#F3F4F6',
          dark: '#D1D5DB',
        },
        success: {
          DEFAULT: '#10B981',
          50: '#ECFDF5',
          100: '#D1FAE5',
          500: '#10B981',
          600: '#059669',
        },
        warning: {
          DEFAULT: '#F59E0B',
          50: '#FFFBEB',
          100: '#FEF3C7',
          500: '#F59E0B',
          600: '#D97706',
        },
        error: {
          DEFAULT: '#EF4444',
          50: '#FEF2F2',
          100: '#FEE2E2',
          500: '#EF4444',
          600: '#DC2626',
        },
        gray: {
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
        },
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        xs: '2.4px',
        sm: '4.8px',
        md: '9.6px',
        lg: '14.4px',
        xl: '19.2px',
        '2xl': '28.8px',
        '3xl': '38.4px',
      },
      borderRadius: {
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
      },
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
      },
    },
  },
  plugins: [],
}
