import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from '@/lib/i18n';

interface BusinessTypeSelectorProps {
  selectedType?: string;
  onSelect: (type: string) => void;
}

export default function BusinessTypeSelector({ selectedType, onSelect }: BusinessTypeSelectorProps) {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const getBusinessTypes = () => [
    { value: 'sole_proprietorship', label: t('institute.soleProprietorship'), description: t('institute.soleProprietorshipDesc') },
    { value: 'partnership', label: t('institute.partnership'), description: t('institute.partnershipDesc') },
    { value: 'corporation', label: t('institute.corporation'), description: t('institute.corporationDesc') },
    { value: 'llc', label: t('institute.limitedLiabilityCompany'), description: t('institute.limitedLiabilityCompanyDesc') },
    { value: 'ngo', label: t('institute.nonGovernmentalOrganization'), description: t('institute.nonGovernmentalOrganizationDesc') },
    { value: 'wellness_center', label: t('institute.wellnessCenter'), description: t('institute.wellnessCenterDesc') },
    { value: 'other', label: t('institute.other'), description: t('institute.otherDesc') }
  ];

  const businessTypes = getBusinessTypes();
  const selectedBusinessType = businessTypes.find(type => type.value === selectedType);

  const handleSelect = (type: string) => {
    onSelect(type);
    setIsModalVisible(false);
  };

  return (
    <View>
      <TouchableOpacity
        onPress={() => setIsModalVisible(true)}
        className="flex-row items-center justify-between p-4 border border-gray-300 rounded-lg bg-white"
      >
        <View className="flex-1">
          {selectedBusinessType ? (
            <View>
              <Text className="text-gray-900 font-medium">
                {selectedBusinessType.label}
              </Text>
              <Text className="text-gray-500 text-sm">
                {selectedBusinessType.description}
              </Text>
            </View>
          ) : (
            <Text className="text-gray-500">{t('institute.selectBusinessType')}</Text>
          )}
        </View>
        <Ionicons name="chevron-down" size={20} color="#9CA3AF" />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View className="flex-1 bg-white">
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
            <Text className="text-lg font-semibold">{t('institute.selectBusinessType')}</Text>
            <TouchableOpacity
              onPress={() => setIsModalVisible(false)}
              className="p-2"
            >
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>

          {/* Business Types List */}
          <ScrollView className="flex-1">
            {businessTypes.map((type) => (
              <TouchableOpacity
                key={type.value}
                onPress={() => handleSelect(type.value)}
                className="flex-row items-center p-4 border-b border-gray-100"
              >
                <View className="flex-1">
                  <Text className="text-gray-900 font-medium mb-1">
                    {type.label}
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    {type.description}
                  </Text>
                </View>
                {selectedType === type.value && (
                  <Ionicons name="checkmark" size={20} color="#059669" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}
