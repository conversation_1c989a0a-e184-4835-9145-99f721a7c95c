# Root .gitignore for Nurture Project

# APK Files (Generated builds)
*.apk
*.aab
Nurture-Wellness-*.apk

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary folders
tmp/
temp/

# Build outputs
build/
dist/
out/

# Docker
.docker/
docker-compose.override.yml
*.sql.backup
postgres_backup.tar.gz
