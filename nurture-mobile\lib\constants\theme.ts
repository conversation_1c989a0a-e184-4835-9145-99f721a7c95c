export const theme = {
  colors: {
    primary: {
      DEFAULT: '#007A5E',
      50: '#E6F7F3',
      100: '#CCEFE7',
      200: '#99DFCF',
      300: '#66CFB7',
      400: '#33BF9F',
      500: '#007A5E',
      600: '#006B52',
      700: '#005C46',
      800: '#004D3A',
      900: '#003E2E',
    },
    secondary: {
      DEFAULT: '#FCD116',
      50: '#FFFDF0',
      100: '#FFFBE0',
      200: '#FFF7C2',
      300: '#FFF3A3',
      400: '#FFEF85',
      500: '#FCD116',
      600: '#E6BC14',
      700: '#D1A712',
      800: '#BC9210',
      900: '#A77D0E',
    },
    accent: {
      DEFAULT: '#CE1126',
      50: '#FCE8EA',
      100: '#F9D1D5',
      200: '#F3A3AB',
      300: '#ED7581',
      400: '#E74757',
      500: '#CE1126',
      600: '#B50E21',
      700: '#9C0C1C',
      800: '#830A17',
      900: '#6A0812',
    },
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: '#1F2937',
    textSecondary: '#6B7280',
    border: '#E5E7EB',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    '2xl': 48,
    '3xl': 64,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 24,
    full: 9999,
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
  },
} as const;

export type Theme = typeof theme;
