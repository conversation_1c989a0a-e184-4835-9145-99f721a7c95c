/**
 * Store Initializer Utility
 * Ensures proper hydration of Zustand stores in APK builds
 * This helps prevent navigation errors caused by slow store hydration
 */

import { useAuthStore } from '../stores/authStore';

/**
 * Ensures auth store is properly hydrated
 * This is particularly important for APK builds where hydration might be slower
 */
export const ensureAuthStoreHydration = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const checkHydration = () => {
      const { isHydrated } = useAuthStore.getState();
      
      if (isHydrated) {
        console.log('✅ Auth store hydration confirmed');
        resolve(true);
        return;
      }

      console.log('🔄 Waiting for auth store hydration...');
      
      // Check again after a short delay
      setTimeout(checkHydration, 100);
    };

    // Start checking immediately
    checkHydration();

    // Fallback timeout for APK builds
    const isAPKBuild = !__DEV__;
    const timeoutDuration = isAPKBuild ? 5000 : 3000;
    
    setTimeout(() => {
      const { isHydrated } = useAuthStore.getState();
      if (!isHydrated) {
        console.warn('⚠️ Auth store hydration timeout, forcing completion');
        // Force hydration by manually setting the state
        useAuthStore.setState({ isHydrated: true });
      }
      resolve(true);
    }, timeoutDuration);
  });
};

/**
 * Initialize all stores for APK builds
 * This ensures proper startup behavior in production builds
 */
export const initializeStoresForAPK = async (): Promise<void> => {
  const isAPKBuild = !__DEV__;
  
  if (!isAPKBuild) {
    console.log('🔧 Development mode: Skipping APK store initialization');
    return;
  }

  console.log('🚀 APK Build: Initializing stores...');
  
  try {
    // Ensure auth store hydration
    await ensureAuthStoreHydration();
    
    console.log('✅ APK Build: Store initialization complete');
  } catch (error) {
    console.error('❌ APK Build: Store initialization failed:', error);
    // Continue anyway - the app should still work
  }
};

/**
 * Get current auth state safely
 * This provides a safe way to access auth state even if hydration is incomplete
 */
export const getSafeAuthState = () => {
  try {
    const state = useAuthStore.getState();
    
    // If not hydrated, return safe defaults
    if (!state.isHydrated) {
      console.log('⚠️ Auth state accessed before hydration, returning safe defaults');
      return {
        isAuthenticated: false,
        isHydrated: false,
        userType: null,
        token: null,
        user: null
      };
    }
    
    return state;
  } catch (error) {
    console.error('❌ Error accessing auth state:', error);
    return {
      isAuthenticated: false,
      isHydrated: false,
      userType: null,
      token: null,
      user: null
    };
  }
};
