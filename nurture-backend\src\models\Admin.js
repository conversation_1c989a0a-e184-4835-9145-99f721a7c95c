const db = require('../config/database');
const bcrypt = require('bcryptjs');

class Admin {
  /**
   * Create a new admin
   */
  static async create({ email, password, firstName, lastName, role = 'admin', permissions = [], createdBy = null }) {
    const hashedPassword = await bcrypt.hash(password, 12);

    try {
      const query = `
        INSERT INTO admins (email, password, first_name, last_name, role, permissions, created_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, email, first_name, last_name, role, permissions, is_active, email_verified, created_at, updated_at
      `;
      
      const values = [email, hashedPassword, firstName, lastName, role, JSON.stringify(permissions), createdBy];
      const result = await db.query(query, values);
      
      return result.rows[0];
    } catch (error) {
      if (error.code === '23505') { // Unique violation
        throw new Error('Email already exists');
      }
      throw error;
    }
  }

  /**
   * Find admin by email
   */
  static async findByEmail(email) {
    try {
      const query = `
        SELECT id, email, password, first_name, last_name, role, permissions, 
               is_active, email_verified, last_login, login_attempts, locked_until,
               password_changed_at, must_change_password, created_at, updated_at
        FROM admins 
        WHERE email = $1
      `;
      
      const result = await db.query(query, [email]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find admin by ID
   */
  static async findById(id) {
    try {
      const query = `
        SELECT id, email, password, first_name, last_name, role, permissions,
               is_active, email_verified, last_login, login_attempts, locked_until,
               password_changed_at, must_change_password, created_at, updated_at
        FROM admins
        WHERE id = $1
      `;

      const result = await db.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify admin password
   */
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * Update last login time
   */
  static async updateLastLogin(id, ipAddress = null) {
    try {
      const query = `
        UPDATE admins 
        SET last_login = CURRENT_TIMESTAMP, login_attempts = 0, locked_until = NULL
        WHERE id = $1
        RETURNING last_login
      `;
      
      const result = await db.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Increment login attempts
   */
  static async incrementLoginAttempts(id) {
    try {
      const query = `
        UPDATE admins 
        SET login_attempts = login_attempts + 1,
            locked_until = CASE 
              WHEN login_attempts >= 4 THEN CURRENT_TIMESTAMP + INTERVAL '30 minutes'
              ELSE locked_until
            END
        WHERE id = $1
        RETURNING login_attempts, locked_until
      `;
      
      const result = await db.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if admin is locked
   */
  static async isLocked(id) {
    try {
      const query = `
        SELECT locked_until 
        FROM admins 
        WHERE id = $1
      `;
      
      const result = await db.query(query, [id]);
      const admin = result.rows[0];
      
      if (!admin || !admin.locked_until) {
        return false;
      }
      
      return new Date(admin.locked_until) > new Date();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all admins with pagination
   */
  static async getAll({ page = 1, limit = 10, search = '', role = '' } = {}) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      const queryParams = [];
      let paramCount = 0;

      if (search) {
        paramCount++;
        const searchParam1 = paramCount;
        paramCount++;
        const searchParam2 = paramCount;
        paramCount++;
        const searchParam3 = paramCount;
        whereClause += ` AND (first_name ILIKE $${searchParam1} OR last_name ILIKE $${searchParam2} OR email ILIKE $${searchParam3})`;
        queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      if (role) {
        paramCount++;
        whereClause += ` AND role = $${paramCount}`;
        queryParams.push(role);
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM admins ${whereClause}`;
      const countResult = await db.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // Get admins
      paramCount++;
      const limitParam = paramCount;
      paramCount++;
      const offsetParam = paramCount;

      const query = `
        SELECT id, email, first_name, last_name, role, permissions, 
               is_active, email_verified, last_login, created_at, updated_at
        FROM admins 
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${limitParam} OFFSET $${offsetParam}
      `;
      
      queryParams.push(limit, offset);
      const result = await db.query(query, queryParams);

      return {
        admins: result.rows,
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update admin
   */
  static async update(id, updates, updatedBy = null) {
    try {
      const allowedFields = ['first_name', 'last_name', 'phone', 'role', 'permissions', 'is_active'];
      const updateFields = [];
      const values = [];
      let paramCount = 0;

      Object.keys(updates).forEach(key => {
        if (allowedFields.includes(key)) {
          paramCount++;
          updateFields.push(`${key} = $${paramCount}`);
          values.push(updates[key]);
        }
      });

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      paramCount++;
      updateFields.push(`updated_by = $${paramCount}`);
      values.push(updatedBy);

      paramCount++;
      values.push(id);

      const query = `
        UPDATE admins 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramCount}
        RETURNING id, email, first_name, last_name, phone, role, permissions, is_active, updated_at
      `;

      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Change admin password
   */
  static async changePassword(id, newPassword, updatedBy = null) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      
      const query = `
        UPDATE admins 
        SET password = $1, password_changed_at = CURRENT_TIMESTAMP, 
            must_change_password = false, updated_by = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
        RETURNING id, email, password_changed_at
      `;

      const result = await db.query(query, [hashedPassword, updatedBy, id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete admin (soft delete by deactivating)
   */
  static async delete(id, deletedBy = null) {
    try {
      const query = `
        UPDATE admins 
        SET is_active = false, updated_by = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING id, email, is_active
      `;

      const result = await db.query(query, [deletedBy, id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Admin;
