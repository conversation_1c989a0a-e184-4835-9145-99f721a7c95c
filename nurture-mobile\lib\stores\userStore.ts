import { create } from 'zustand';
import { User } from '../types';
import { mockUserApi } from '../api/mockApi';

interface UserState {
  profile: User | null;
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
}

interface UserActions {
  fetchProfile: (userId: string) => Promise<void>;
  updateProfile: (userId: string, userData: Partial<User>) => Promise<boolean>;
  setProfile: (user: User) => void;
  clearError: () => void;
}

type UserStore = UserState & UserActions;

export const useUserStore = create<UserStore>((set, get) => ({
  // Initial state
  profile: null,
  isLoading: false,
  isUpdating: false,
  error: null,

  // Actions
  fetchProfile: async (userId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await mockUserApi.getUserProfile(userId);
      
      if (response.success) {
        set({
          profile: response.data,
          isLoading: false
        });
      } else {
        set({
          error: response.message || 'Failed to fetch profile',
          isLoading: false
        });
      }
    } catch (error) {
      set({
        error: 'Network error. Please try again.',
        isLoading: false
      });
    }
  },

  updateProfile: async (userId: string, userData: Partial<User>) => {
    set({ isUpdating: true, error: null });
    
    try {
      const response = await mockUserApi.updateProfile(userId, userData);
      
      if (response.success) {
        set({
          profile: response.data,
          isUpdating: false
        });
        return true;
      } else {
        set({
          error: response.message || 'Failed to update profile',
          isUpdating: false
        });
        return false;
      }
    } catch (error) {
      set({
        error: 'Network error. Please try again.',
        isUpdating: false
      });
      return false;
    }
  },

  setProfile: (user: User) => {
    set({ profile: user });
  },

  clearError: () => {
    set({ error: null });
  }
}));
