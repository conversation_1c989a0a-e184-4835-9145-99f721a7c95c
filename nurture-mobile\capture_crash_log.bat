@echo off
echo ================================
echo APK Crash Log Capture Tool
echo ================================
echo.
echo Instructions:
echo 1. Make sure your phone is connected via USB
echo 2. USB Debugging is enabled on your phone
echo 3. The APK is installed on your phone
echo.
echo Press any key to start capturing logs...
pause >nul

echo.
echo Clearing old logs...
adb logcat -c

echo.
echo Starting log capture...
echo Now open the app and click "Verify Now" to reproduce the crash
echo Press Ctrl+C when the app crashes to stop logging
echo.
echo Saving to: crash_log.txt
echo.

adb logcat -v time *:E *:F ReactNativeJS:* AndroidRuntime:* > crash_log.txt

echo.
echo Log saved to crash_log.txt
echo.
pause
