#!/bin/sh

# Docker entrypoint script for Nurture Backend
# This script helps debug environment variable loading and ensures proper startup

echo "🐳 Starting Nurture Backend in Docker..."
echo ""

# Check if .env file exists
if [ -f .env ]; then
    echo "✅ .env file found"
    echo "📋 Environment file contents (sensitive values masked):"
    sed 's/=.*/=***/' .env | head -10
else
    echo "⚠️  .env file not found"
    echo "📋 Available files in current directory:"
    ls -la
fi

echo ""
echo "🔍 Current environment variables:"
echo "   NODE_ENV: ${NODE_ENV:-undefined}"
echo "   PORT: ${PORT:-undefined}"
echo "   DB_HOST: ${DB_HOST:-undefined}"
echo "   DB_PORT: ${DB_PORT:-undefined}"
echo "   DB_NAME: ${DB_NAME:-undefined}"
echo "   DB_USER: ${DB_USER:-undefined}"
echo "   DB_PASSWORD: ${DB_PASSWORD:+***}"
echo "   JWT_SECRET: ${JWT_SECRET:+***}"
echo ""

# Test database connectivity before starting the application
echo "🔌 Testing database connectivity..."
node -e "
const config = require('./src/config/env');
const { Pool } = require('pg');

const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  connectionTimeoutMillis: 5000
});

pool.connect()
  .then(client => {
    console.log('✅ Database connection successful');
    client.release();
    pool.end();
  })
  .catch(err => {
    console.log('❌ Database connection failed:', err.message);
    pool.end();
    process.exit(1);
  });
"

echo ""
echo "🚀 Starting application..."

# Execute the main command
exec "$@"
