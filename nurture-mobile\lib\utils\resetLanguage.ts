/**
 * Utility to reset language and force fresh detection
 * This can be called when the app needs to reset to default language
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLanguageStore } from '../stores/languageStore';

/**
 * Reset all language settings to French (default)
 * This clears all cached language data and sets the app to French
 */
export const resetLanguageToFrench = async (): Promise<void> => {
  try {
    console.log('🔄 Resetting language to French (default)...');

    // Clear AsyncStorage language data
    await AsyncStorage.removeItem('language-storage');
    await AsyncStorage.removeItem('auth_manual_language');
    console.log('✅ Cleared AsyncStorage language data');

    // Reset language store to French
    const { resetLanguageSettings, setLanguage } = useLanguageStore.getState();
    resetLanguageSettings();
    setLanguage('user', 'fr', false); // isManual=false
    setLanguage('institute', 'fr', false); // isManual=false
    console.log('✅ Reset language store to French');

    console.log('✅ Language reset complete - app should now be in French');
  } catch (error) {
    console.error('❌ Failed to reset language:', error);
    throw error;
  }
};

/**
 * Force clear ALL language data and trigger fresh location detection
 * Use this when language detection seems stuck
 */
export const forceResetAndDetect = async (): Promise<void> => {
  try {
    console.log('🔄 Force resetting ALL language data...');

    // Clear ALL AsyncStorage keys related to language
    const allKeys = await AsyncStorage.getAllKeys();
    const languageKeys = allKeys.filter(key =>
      key.includes('language') ||
      key.includes('auth') ||
      key.includes('location')
    );

    console.log('🗑️ Removing keys:', languageKeys);
    await AsyncStorage.multiRemove(languageKeys);
    console.log('✅ Cleared all language-related AsyncStorage data');

    // Reset language store completely
    const store = useLanguageStore.getState();
    store.resetLanguageSettings();
    store.clearManualOverride();

    console.log('✅ Force reset complete - restart app to trigger fresh location detection');
  } catch (error) {
    console.error('❌ Failed to force reset:', error);
    throw error;
  }
};

/**
 * Backward compatibility - alias for resetLanguageToFrench
 * @deprecated Use resetLanguageToFrench instead
 */
export const resetLanguageToEnglish = resetLanguageToFrench;

/**
 * Check current language settings
 * Useful for debugging language issues
 */
export const checkLanguageSettings = async (): Promise<void> => {
  try {
    console.log('🔍 Checking language settings...');
    
    // Check AsyncStorage
    const languageStorage = await AsyncStorage.getItem('language-storage');
    const authManualLanguage = await AsyncStorage.getItem('auth_manual_language');
    
    console.log('📦 AsyncStorage language-storage:', languageStorage);
    console.log('📦 AsyncStorage auth_manual_language:', authManualLanguage);
    
    // Check language store
    const { userLanguage, instituteLanguage, currentAccountType, detectedLanguage, locationBasedLanguage } = useLanguageStore.getState();
    
    console.log('🗣️ Language Store:');
    console.log('  - userLanguage:', userLanguage);
    console.log('  - instituteLanguage:', instituteLanguage);
    console.log('  - currentAccountType:', currentAccountType);
    console.log('  - detectedLanguage:', detectedLanguage);
    console.log('  - locationBasedLanguage:', locationBasedLanguage);
  } catch (error) {
    console.error('❌ Failed to check language settings:', error);
  }
};

