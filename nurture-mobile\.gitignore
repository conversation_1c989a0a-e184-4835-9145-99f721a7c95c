# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Android Build Artifacts
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/app/release/
android/app/debug/
*.apk
*.aab

# iOS Build Artifacts
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# Network Security Config (if contains sensitive data)
android/app/src/main/res/xml/network_security_config.xml

# Generated files
android/app/src/main/assets/
android/app/src/main/res/drawable-hdpi/
android/app/src/main/res/drawable-mdpi/
android/app/src/main/res/drawable-xhdpi/
android/app/src/main/res/drawable-xxhdpi/
android/app/src/main/res/drawable-xxxhdpi/

# Keystore files (sensitive)
*.keystore
release.keystore
debug.keystore
android/app/debug.keystore

# Expo generated files
.expo-shared/
