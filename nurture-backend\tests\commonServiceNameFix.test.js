const assert = require('assert');

/**
 * Test Suite: Common Service Name Fix
 * Tests that when booking appointments with "no preference" staff,
 * the actual service name is displayed instead of defaulting to "Massage Therapy"
 */

// Mock database query function
const mockPoolQuery = async (query, params) => {
  // Simulate database responses for common service pricing queries
  if (query.includes('institute_common_service_pricing')) {
    // Extract the institute ID and service index from params
    const instituteId = params[0];
    const serviceIndex = params[1] || 0;
    
    // Mock service names for different institutes
    const mockServices = {
      '1': ['Physiotherapy', 'Acupuncture', 'Massage Therapy'],
      '2': ['ENT Consultation', 'Thyroid Treatment', 'General Checkup'],
      '3': ['Dental Cleaning', 'Root Canal', 'Orthodontics']
    };
    
    const services = mockServices[instituteId] || [];
    if (serviceIndex < services.length) {
      return {
        rows: [{ service_name: services[serviceIndex] }]
      };
    }
    return { rows: [] };
  }
  
  // For other queries, return empty
  return { rows: [] };
};

// Test helper to parse common service ID
function parseCommonServiceId(serviceId) {
  const parts = serviceId.split('_');
  if (parts.length >= 3) {
    return {
      instituteId: parts[1],
      serviceIndex: parseInt(parts[2])
    };
  }
  return null;
}

// Run tests
if (require.main === module) {
  console.log('🧪 Running Common Service Name Fix Tests...\n');

  let passedTests = 0;
  let failedTests = 0;

  const runTest = (testName, testFn) => {
    try {
      console.log(`\n📋 ${testName}`);
      testFn();
      console.log(`✅ PASSED`);
      passedTests++;
    } catch (error) {
      console.error(`❌ FAILED: ${error.message}`);
      failedTests++;
    }
  };

  // Test 1: Parse common service ID correctly
  runTest('Parse common service ID format', () => {
    const serviceId = 'common_1_0';
    const parsed = parseCommonServiceId(serviceId);
    
    assert.strictEqual(parsed.instituteId, '1', 'Institute ID should be 1');
    assert.strictEqual(parsed.serviceIndex, 0, 'Service index should be 0');
  });

  // Test 2: Parse common service ID with different institute
  runTest('Parse common service ID with different institute', () => {
    const serviceId = 'common_2_1';
    const parsed = parseCommonServiceId(serviceId);
    
    assert.strictEqual(parsed.instituteId, '2', 'Institute ID should be 2');
    assert.strictEqual(parsed.serviceIndex, 1, 'Service index should be 1');
  });

  // Test 3: Verify service name retrieval for Physiotherapy
  runTest('Retrieve Physiotherapy service name from database', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['1', 0]
    );
    
    assert.strictEqual(result.rows.length, 1, 'Should return one row');
    assert.strictEqual(result.rows[0].service_name, 'Physiotherapy', 'Service name should be Physiotherapy');
  });

  // Test 4: Verify service name retrieval for ENT Consultation
  runTest('Retrieve ENT Consultation service name from database', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['2', 0]
    );
    
    assert.strictEqual(result.rows.length, 1, 'Should return one row');
    assert.strictEqual(result.rows[0].service_name, 'ENT Consultation', 'Service name should be ENT Consultation');
  });

  // Test 5: Verify service name retrieval for Thyroid Treatment
  runTest('Retrieve Thyroid Treatment service name from database', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['2', 1]
    );
    
    assert.strictEqual(result.rows.length, 1, 'Should return one row');
    assert.strictEqual(result.rows[0].service_name, 'Thyroid Treatment', 'Service name should be Thyroid Treatment');
  });

  // Test 6: Verify service name retrieval for Dental Cleaning
  runTest('Retrieve Dental Cleaning service name from database', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['3', 0]
    );
    
    assert.strictEqual(result.rows.length, 1, 'Should return one row');
    assert.strictEqual(result.rows[0].service_name, 'Dental Cleaning', 'Service name should be Dental Cleaning');
  });

  // Test 7: Verify fallback when service not found
  runTest('Fallback when service index out of range', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['1', 999]
    );
    
    assert.strictEqual(result.rows.length, 0, 'Should return no rows for out of range index');
  });

  // Test 8: Verify common service ID format validation
  runTest('Validate common service ID format', () => {
    const validId = 'common_1_0';
    const isValid = validId.startsWith('common_') && validId.split('_').length >= 3;
    
    assert.strictEqual(isValid, true, 'Common service ID should be valid');
  });

  // Test 9: Verify custom service ID is not treated as common
  runTest('Distinguish custom service ID from common service ID', () => {
    const customId = '123';
    const isCommon = customId.startsWith('common_');
    
    assert.strictEqual(isCommon, false, 'Custom service ID should not be treated as common');
  });

  // Test 10: Verify service name is not defaulting to "Massage Therapy"
  runTest('Ensure service name is not defaulting to Massage Therapy', async () => {
    const result = await mockPoolQuery(
      'SELECT service_name FROM institute_common_service_pricing WHERE institute_id = $1 ORDER BY service_name LIMIT 1 OFFSET $2',
      ['2', 0]
    );
    
    assert.notStrictEqual(result.rows[0].service_name, 'Massage Therapy', 'Service name should not default to Massage Therapy');
    assert.strictEqual(result.rows[0].service_name, 'ENT Consultation', 'Service name should be the actual service');
  });

  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Total: ${passedTests + failedTests}`);
  
  if (failedTests === 0) {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some tests failed!');
    process.exit(1);
  }
}

module.exports = { parseCommonServiceId, mockPoolQuery };

