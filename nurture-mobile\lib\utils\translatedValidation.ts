import { getTranslation, Language } from '@/lib/i18n';

/**
 * Translates validation error messages based on the selected language
 * This wrapper translates the hardcoded English messages from validation.ts
 */

export const translateValidationMessage = (
  message: string,
  language: Language = 'en'
): string => {
  // Map of English messages to translation keys
  const messageMap: Record<string, { key: string; variables?: Record<string, any> }> = {
    'Country code is missing. Please select a country first.': {
      key: 'institute.countryCodeMissing'
    },
    'Country code is missing. Please include the country code (e.g., +1, +44).': {
      key: 'institute.countryCodeMissingInclude'
    },
    'Please enter a valid phone number with country code (e.g., +1234567890)': {
      key: 'institute.invalidPhoneFormat'
    },
    'Please enter a valid phone number with country code': {
      key: 'institute.invalidPhoneFormat'
    },
    'Please enter a valid phone number (7-15 digits) or include country code (e.g., +1234567890)': {
      key: 'institute.invalidPhoneFormatFlexible'
    }
  };

  // Check for exact matches first
  if (messageMap[message]) {
    const { key, variables } = messageMap[message];
    return getTranslation(key, language, variables);
  }

  // Check for pattern matches (messages with dynamic content)
  // Pattern: "Invalid digit count for {{country}} ({{code}}). Expected {{expected}} digits, but got {{got}} digits."
  const digitCountMatch = message.match(
    /Invalid digit count for (.+?) \((.+?)\)\. Expected (\d+) digits, but got (\d+) digits\./
  );
  if (digitCountMatch) {
    const [, country, code, expected, got] = digitCountMatch;
    return getTranslation('institute.invalidDigitCount', language, {
      country,
      code,
      expected,
      got
    });
  }

  // Pattern: "Invalid {{country}} phone number format. Expected: {{format}}"
  const formatMatch = message.match(/Invalid (.+?) phone number format\. Expected: (.+?)\./);
  if (formatMatch) {
    const [, country, format] = formatMatch;
    return getTranslation('institute.invalidPhoneFormatCountry', language, {
      country,
      format
    });
  }

  // Pattern: "Most countries require 7-12 digits after the country code, but got {{got}} digits."
  const generalFormatMatch = message.match(
    /Most countries require 7-12 digits after the country code, but got (\d+) digits\./
  );
  if (generalFormatMatch) {
    const [, got] = generalFormatMatch;
    return getTranslation('institute.invalidPhoneFormatGeneral', language, { got });
  }

  // Pattern: "Warning: Expected {{expected}} for {{selectedCountry}}, but got {{got}} for {{detectedCountry}}. You can still continue."
  const warningMatch = message.match(
    /Warning: Expected (.+?) for (.+?), but got (.+?) for (.+?)\. You can still continue\./
  );
  if (warningMatch) {
    const [, expected, selectedCountry, got, detectedCountry] = warningMatch;
    return getTranslation('institute.phoneWarningMismatch', language, {
      expected,
      selectedCountry,
      got,
      detectedCountry
    });
  }

  // If no translation found, return the original message
  return message;
};

/**
 * Translates validation messages in a validation result object
 */
export const translateValidationResult = (
  result: { isValid: boolean; message?: string; isWarning?: boolean },
  language: Language = 'en'
): { isValid: boolean; message?: string; isWarning?: boolean } => {
  if (result.message) {
    return {
      ...result,
      message: translateValidationMessage(result.message, language)
    };
  }
  return result;
};

