import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface TimezoneNotificationProps {
  instituteName: string;
  country: string;
  timezone: string;
  currentTime?: string;
}

export default function TimezoneNotification({ 
  instituteName, 
  country, 
  timezone, 
  currentTime 
}: TimezoneNotificationProps) {
  // Get a user-friendly timezone display name
  const getTimezoneDisplayName = (country: string, timezone: string): string => {
    try {
      const now = new Date();
      const timeInTimezone = now.toLocaleString('en-US', { 
        timeZone: timezone,
        timeZoneName: 'short'
      });
      
      // Extract timezone abbreviation (e.g., "WAT", "EST", etc.)
      const timezoneName = timeInTimezone.split(' ').pop() || timezone;
      
      return `${country} Time (${timezoneName})`;
    } catch (error) {
      console.error(`❌ Error getting timezone display name for ${timezone}:`, error);
      return `${country} Time`;
    }
  };

  const timezoneDisplay = getTimezoneDisplayName(country, timezone);

  return (
    <View className="mx-6 mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex-row items-start">
      <Ionicons 
        name="information-circle" 
        size={20} 
        color="#DC2626" 
        className="mr-3 mt-0.5" 
      />
      <View className="flex-1">
        <Text className="text-red-700 font-medium text-sm leading-5">
          The institute "{instituteName}" is located in {country}. 
          Time slots are displayed in {timezoneDisplay}.
        </Text>
        {currentTime && (
          <Text className="text-red-600 text-xs mt-1">
            Current time at institute: {currentTime}
          </Text>
        )}
      </View>
    </View>
  );
}
