// User types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  profilePictureUrl?: string
  isActive: boolean
  emailVerified: boolean
  isAdmin?: boolean
  createdAt: string
  updatedAt: string
}

// Admin types
export interface Admin {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  role: 'super_admin' | 'admin' | 'moderator'
  permissions: string[]
  is_active: boolean
  email_verified: boolean
  last_login?: string
  password_changed_at?: string
  must_change_password: boolean
  created_at: string
  updated_at: string
}

// Institute types
export interface Institute {
  id: string
  ownerId: string
  name: string
  businessName?: string
  description?: string
  email: string
  phone: string
  website?: string
  businessRegistrationNumber?: string
  taxId?: string
  businessType?: 'sole_proprietorship' | 'partnership' | 'corporation' | 'llc' | 'ngo' | 'other'
  status: 'pending' | 'under_review' | 'verified' | 'rejected' | 'suspended' | 'inactive'
  verificationStatus: 'unverified' | 'pending' | 'verified' | 'rejected'
  address?: Address
  openingHours?: OpeningHours
  servicesOffered?: string[]
  priceRange?: '$' | '$$' | '$$$' | '$$$$'
  logoUrl?: string
  coverImageUrl?: string
  galleryImages?: string[]
  rating: number
  reviewCount: number
  isActive: boolean
  isFeatured: boolean
  createdAt: string
  updatedAt: string
  is_reregistered?: boolean
  reregistered_at?: string
  owner?: User
}

// Address type
export interface Address {
  street: string
  city: string
  state: string
  country: string
  postalCode: string
  latitude?: number
  longitude?: number
}

// Opening hours type
export interface OpeningHours {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

export interface DaySchedule {
  open: string
  close: string
  closed: boolean
}

// Dashboard stats type
export interface DashboardStats {
  totalInstitutes: number
  pendingApprovals: number
  verifiedInstitutes: number
  rejectedInstitutes: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'institute_registered' | 'institute_approved' | 'institute_rejected' | 'user_registered'
  title: string
  description: string
  timestamp: string
  instituteId?: string
  userId?: string
}

// Form types
export interface LoginForm {
  email: string
  password: string
  rememberMe?: boolean
}

export interface InstituteFilters {
  status?: string
  verificationStatus?: string
  businessType?: string
  search?: string
  page?: number
  limit?: number
}

// API Response types
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface ApiError {
  message: string
  errors?: string[]
  status?: number
}
