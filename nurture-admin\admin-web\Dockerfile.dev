# Development Dockerfile for React + Vite application
FROM node:22-alpine

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies (including dev dependencies)
RUN npm ci

# Copy the rest of the application code
COPY . .

# Create a non-root user to run the application
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nurture -u 1001

# Change ownership of the app directory to the nodejs user
RUN chown -R nurture:nodejs /app
USER nurture

# Expose the port the app runs on (Vite default is 5173)
EXPOSE 5173

# Define environment variable
ENV NODE_ENV=development

# Command to run the development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
