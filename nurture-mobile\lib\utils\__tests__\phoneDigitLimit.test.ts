import { extractPhoneDigits, validatePhoneDigitLimit } from '../validation';

describe('Phone Digit Limit Validation', () => {
  describe('extractPhoneDigits', () => {
    it('should extract digits from India phone number with country code', () => {
      const result = extractPhoneDigits('+91 9876543210', 'India');
      expect(result).toBe('9876543210');
    });

    it('should extract digits from Cameroon phone number with country code', () => {
      const result = extractPhoneDigits('+237 670123456', 'Cameroon');
      expect(result).toBe('670123456');
    });

    it('should handle phone number without country code', () => {
      const result = extractPhoneDigits('9876543210', 'India');
      expect(result).toBe('9876543210');
    });

    it('should handle phone number with formatting characters', () => {
      const result = extractPhoneDigits('+91 (*************', 'India');
      expect(result).toBe('9876543210');
    });

    it('should return empty string for empty input', () => {
      const result = extractPhoneDigits('', 'India');
      expect(result).toBe('');
    });

    it('should handle 0 prefix for UK numbers', () => {
      const result = extractPhoneDigits('02079460958', 'United Kingdom');
      expect(result).toBe('02079460958');
    });
  });

  describe('validatePhoneDigitLimit', () => {
    // NEW BEHAVIOR: Allow 7-15 digits for ANY country (flexible validation)
    // This allows users to keep their original phone number when changing country/city

    it('should allow valid India phone number (10 digits)', () => {
      const result = validatePhoneDigitLimit('+91 9876543210', 'India');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15); // Now allows up to 15 digits for any country
      expect(result.currentDigits).toBe(10);
    });

    it('should allow India phone number with 11 digits (flexible validation)', () => {
      // NEW: This is now allowed (was rejected before)
      const result = validatePhoneDigitLimit('+91 98765432101', 'India');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15);
      expect(result.currentDigits).toBe(11);
    });

    it('should allow valid Cameroon phone number (9 digits)', () => {
      const result = validatePhoneDigitLimit('+237 670123456', 'Cameroon');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15); // Now allows up to 15 digits for any country
      expect(result.currentDigits).toBe(9);
    });

    it('should allow Cameroon phone number with 10 digits (flexible validation)', () => {
      // NEW: This is now allowed (was rejected before)
      const result = validatePhoneDigitLimit('+237 6701234567', 'Cameroon');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15);
      expect(result.currentDigits).toBe(10);
    });

    it('should allow valid US phone number (10 digits)', () => {
      const result = validatePhoneDigitLimit('*************', 'United States');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15); // Now allows up to 15 digits for any country
    });

    it('should allow US phone number with 11 digits (flexible validation)', () => {
      // NEW: This is now allowed (was rejected before)
      const result = validatePhoneDigitLimit('*************8', 'United States');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15);
    });

    it('should return valid for unselected country', () => {
      const result = validatePhoneDigitLimit('*************', 'Select Country');
      expect(result.isValid).toBe(true);
    });

    it('should return valid for empty country', () => {
      const result = validatePhoneDigitLimit('*************', '');
      expect(result.isValid).toBe(true);
    });

    it('should handle Brazil phone number (11 digits)', () => {
      const result = validatePhoneDigitLimit('+55 11987654321', 'Brazil');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15); // Now allows up to 15 digits for any country
    });

    it('should allow Brazil phone number with 12 digits (flexible validation)', () => {
      // NEW: This is now allowed (was rejected before)
      const result = validatePhoneDigitLimit('+55 119876543210', 'Brazil');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15);
    });

    it('should reject phone number with less than 7 digits', () => {
      const result = validatePhoneDigitLimit('*********', 'United States');
      expect(result.isValid).toBe(false);
      expect(result.currentDigits).toBe(6);
    });

    it('should reject phone number with more than 15 digits', () => {
      const result = validatePhoneDigitLimit('*********7890123456', 'United States');
      expect(result.isValid).toBe(false);
      expect(result.currentDigits).toBe(16);
    });

    it('should allow India phone number when country is changed to Cameroon (flexible validation)', () => {
      // NEW: This is the main fix - users can keep their original phone number when changing country
      const result = validatePhoneDigitLimit('+91 9591139939', 'Cameroon');
      expect(result.isValid).toBe(true);
      expect(result.maxDigits).toBe(15);
      expect(result.currentDigits).toBe(10);
    });
  });
});

