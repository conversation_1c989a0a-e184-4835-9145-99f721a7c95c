import React from 'react';
import { Switch as RNSwitch, View, Text, SwitchProps as RNS<PERSON>Props } from 'react-native';
import { cn } from '@/lib/utils/cn';

interface SwitchProps extends Omit<RNSwitchProps, 'value' | 'onValueChange'> {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  descriptionClassName?: string;
  size?: 'sm' | 'md' | 'lg';
}

const switchSizes = {
  sm: { transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] },
  md: { transform: [{ scaleX: 1 }, { scaleY: 1 }] },
  lg: { transform: [{ scaleX: 1.2 }, { scaleY: 1.2 }] },
};

const labelSizes = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
};

const descriptionSizes = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
};

export function Switch({
  checked,
  onCheckedChange,
  label,
  description,
  disabled = false,
  className,
  labelClassName,
  descriptionClassName,
  size = 'md',
  ...props
}: SwitchProps) {
  const handleValueChange = (value: boolean) => {
    if (!disabled) {
      onCheckedChange(value);
    }
  };

  if (label || description) {
    return (
      <View
        className={cn(
          'flex-row items-center justify-between',
          disabled && 'opacity-50',
          className
        )}
      >
        <View className="flex-1 mr-4">
          {label && (
            <Text
              className={cn(
                'text-gray-900 font-medium',
                labelSizes[size],
                disabled && 'text-gray-400',
                labelClassName
              )}
            >
              {label}
            </Text>
          )}
          {description && (
            <Text
              className={cn(
                'text-gray-500 mt-0.5',
                descriptionSizes[size],
                disabled && 'text-gray-400',
                descriptionClassName
              )}
            >
              {description}
            </Text>
          )}
        </View>
        <RNSwitch
          value={checked}
          onValueChange={handleValueChange}
          disabled={disabled}
          trackColor={{ false: '#E5E7EB', true: '#10B981' }}
          thumbColor={checked ? '#FFFFFF' : '#F3F4F6'}
          ios_backgroundColor="#E5E7EB"
          style={switchSizes[size]}
          {...props}
        />
      </View>
    );
  }

  return (
    <RNSwitch
      value={checked}
      onValueChange={handleValueChange}
      disabled={disabled}
      trackColor={{ false: '#E5E7EB', true: '#10B981' }}
      thumbColor={checked ? '#FFFFFF' : '#F3F4F6'}
      ios_backgroundColor="#E5E7EB"
      style={switchSizes[size]}
      className={className}
      {...props}
    />
  );
}
