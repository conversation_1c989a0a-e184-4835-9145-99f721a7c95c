import { useState, useEffect, useRef } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Card, CardContent, Button, Input, Badge } from '../components/ui'
import { adminApi } from '../lib/api'

export function Users() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [searchInput, setSearchInput] = useState('') // Local state for input value
  const [statusFilter, setStatusFilter] = useState(searchParams.get('status') || 'all')
  const [userTypeFilter, setUserTypeFilter] = useState('all') // New state for user type filter
  const [users, setUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [exporting, setExporting] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [totalActiveUsers, setTotalActiveUsers] = useState(0)
  const [totalVerifiedUsers, setTotalVerifiedUsers] = useState(0)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Fetch user stats for the stats cards
  const fetchUserStats = async () => {
    try {
      // Get all active users count
      const activeResponse = await adminApi.getUsers({
        page: 1,
        limit: 1, // We only need the total count
        isActive: 'true'
      })

      // Get all verified users count
      const verifiedResponse = await adminApi.getUsers({
        page: 1,
        limit: 1, // We only need the total count
        emailVerified: 'true'
      })

      if (activeResponse.success && activeResponse.data) {
        setTotalActiveUsers(activeResponse.data.total)
      }

      if (verifiedResponse.success && verifiedResponse.data) {
        setTotalVerifiedUsers(verifiedResponse.data.total)
      }
    } catch (err) {
      console.error('Error fetching user stats:', err)
    }
  }

  // Fetch users from API with server-side filtering
  const fetchUsers = async (isSearching = false) => {
    try {
      // Only show loading spinner for initial load, not for search operations
      if (!isSearching) {
        setLoading(true)
      }
      setError(null)

      const response = await adminApi.getUsers({
        page: currentPage,
        limit: 50,
        search: searchTerm,
        isActive: statusFilter === 'active' ? 'true' : statusFilter === 'inactive' ? 'false' : undefined,
        emailVerified: statusFilter === 'verified' ? 'true' : statusFilter === 'unverified' ? 'false' : undefined,
        userType: userTypeFilter === 'all' ? undefined : userTypeFilter
      })

      if (response.success && response.data) {
        setUsers(response.data.users)
        setTotalUsers(response.data.total)
        setTotalPages(response.data.totalPages)
      } else {
        setError('Failed to fetch users')
      }
    } catch (err: any) {
      console.error('Error fetching users:', err)
      const errorMessage = err.code === 'ECONNABORTED'
        ? 'Request timeout. Please check your connection and try again.'
        : err.response?.data?.message || 'Failed to fetch users. Please try again.'
      setError(errorMessage)
    } finally {
      if (!isSearching) {
        setLoading(false)
      }
    }
  }

  // Debounce search input to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchTerm(searchInput) // Update the actual search term after debounce
      setCurrentPage(1) // Reset to first page when search changes
    }, 300) // 300ms debounce

    return () => clearTimeout(timeoutId)
  }, [searchInput])

  // Fetch users when search term changes (after debounce)
  useEffect(() => {
    fetchUsers(true) // Pass true to indicate this is a search operation
  }, [searchTerm])

  // Fetch users when filters or page changes (no debounce needed)
  useEffect(() => {
    fetchUsers()
  }, [statusFilter, userTypeFilter, currentPage])

  // Fetch user stats on component mount
  useEffect(() => {
    fetchUserStats()
  }, [])

  // Handle user type filter change
  const handleUserTypeFilterChange = (userType: string) => {
    setUserTypeFilter(userType)
    setCurrentPage(1) // Reset to first page when filtering
  }

  // Handle status filter change
  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status)
    setCurrentPage(1) // Reset to first page when filtering
  }

  const handleToggleStatus = async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId)
      if (!user) return

      const newStatus = !user.isActive

      // Call API to update user status
      const response = await adminApi.updateUserStatus(userId, newStatus)

      if (response.success) {
        // Update local state
        setUsers(users.map(u =>
          u.id === userId ? { ...u, isActive: newStatus } : u
        ))
      } else {
        setError('Failed to update user status')
      }
    } catch (err: any) {
      console.error('Error updating user status:', err)
      setError(err.response?.data?.message || 'Failed to update user status')
    }
  }

  const handleExport = async (format: 'csv' | 'excel' = 'csv') => {
    try {
      setExporting(true)
      console.log('🔄 Starting export with params:', {
        status: statusFilter !== 'all' ? statusFilter : undefined,
        search: searchTerm || undefined,
        userType: userTypeFilter !== 'all' ? userTypeFilter : undefined,
        format
      })

      const blob = await adminApi.exportUsers({
        status: statusFilter !== 'all' ? statusFilter : undefined,
        search: searchTerm || undefined,
        userType: userTypeFilter !== 'all' ? userTypeFilter : undefined,
        format
      })

      console.log('✅ Export successful, blob size:', blob.size)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      // Show success message
      alert(`Successfully exported ${users.length} users!`)
    } catch (err: any) {
      console.error('❌ Error exporting users:', err)
      console.error('Error response:', err.response)

      let errorMessage = 'Failed to export users data'
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message
      } else if (err.message) {
        errorMessage = err.message
      }

      setError(`Export failed: ${errorMessage}`)
    } finally {
      setExporting(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Users</h1>
            <p className="text-gray-600 mt-2">Loading users...</p>
          </div>
        </div>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Users</h1>
            <p className="text-red-600 mt-2">{error}</p>
          </div>
          <Button onClick={() => fetchUsers()}>
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Users</h1>
          <p className="text-sm text-gray-600 mt-1">
            Manage user accounts and permissions ({totalUsers} total users, showing {users.length} on this page)
          </p>
        </div>
        <Button
          onClick={() => handleExport('csv')}
          disabled={exporting}
        >
          {exporting ? 'Exporting...' : 'Export Users'}
        </Button>
      </div>

      {/* Filters */}
      <Card variant="elevated">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1">
              <Input
                ref={searchInputRef}
                placeholder="Search users by name or email..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                leftIcon={
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />
            </div>
            <div className="sm:w-36">
              <select
                value={statusFilter}
                onChange={(e) => handleStatusFilterChange(e.target.value)}
                className="block w-full rounded-lg border border-gray-300 bg-white px-2 py-2 text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="all">All Users</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="verified">Email Verified</option>
                <option value="unverified">Email Unverified</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Type Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => handleUserTypeFilterChange('all')}
          className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            userTypeFilter === 'all'
              ? 'bg-white text-primary shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          All Users
        </button>
        <button
          onClick={() => handleUserTypeFilterChange('institute')}
          className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            userTypeFilter === 'institute'
              ? 'bg-white text-primary shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Institute Users
        </button>
        <button
          onClick={() => handleUserTypeFilterChange('normal')}
          className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            userTypeFilter === 'normal'
              ? 'bg-white text-primary shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Normal Users
        </button>
      </div>

      {/* Users List */}
      <div className="space-y-3">
        {users.length === 0 ? (
          <Card variant="elevated">
            <CardContent className="p-9 text-center">
              <p className="text-gray-500">No users found</p>
            </CardContent>
          </Card>
        ) : (
          users.map((user) => (
            <Card key={user.id} variant="elevated">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-9 h-9 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary font-semibold text-sm">
                        {(user.firstName || 'U').charAt(0)}{(user.lastName || 'U').charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold text-gray-900">
                        {user.firstName || 'Unknown'} {user.lastName || 'User'}
                      </h3>
                      <p className="text-xs text-gray-600">
                        {user.email} {user.phone && `• ${user.phone}`}
                      </p>
                      {(user.isInstituteOwner || user.isInstituteStaff) && user.instituteName && (
                        <p className="text-xs text-blue-600 font-medium">
                          Institute: {user.instituteName}
                        </p>
                      )}
                      <p className="text-xs text-gray-500 mt-1">
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                        {user.lastLogin && (
                          <span> • Last login: {new Date(user.lastLogin).toLocaleDateString()}</span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="flex flex-col items-end space-y-1">
                      <div className="flex flex-wrap gap-1 justify-end">
                        <Badge variant={user.isActive ? 'success' : 'error'}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                        <Badge variant={user.emailVerified ? 'success' : 'warning'}>
                          {user.emailVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                        {user.isInstituteOwner && (
                          <Badge variant="info">
                            Institute Owner
                          </Badge>
                        )}
                        {user.isInstituteStaff && !user.isInstituteOwner && (
                          <Badge variant="warning">
                            Institute Staff
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/users/${user.id}`)}
                      >
                        View Profile
                      </Button>
                      <Button
                        variant={user.isActive ? 'danger' : 'primary'}
                        size="sm"
                        onClick={() => handleToggleStatus(user.id)}
                      >
                        {user.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-4">
          <Button
            variant="outline"
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Next
          </Button>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="elevated">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-gray-900">{totalUsers}</div>
            <div className="text-sm text-gray-600">Total Users</div>
          </CardContent>
        </Card>
        <Card variant="elevated">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600">
              {totalActiveUsers}
            </div>
            <div className="text-sm text-gray-600">Active Users</div>
          </CardContent>
        </Card>
        <Card variant="elevated">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600">
              {totalVerifiedUsers}
            </div>
            <div className="text-sm text-gray-600">Verified Users</div>
          </CardContent>
        </Card>
        <Card variant="elevated">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-yellow-600">
              {users.filter(u => u.lastLogin && new Date(u.lastLogin) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length}
            </div>
            <div className="text-sm text-gray-600">Active This Week (Current Page)</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
