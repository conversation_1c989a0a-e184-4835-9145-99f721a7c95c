const db = require('../config/database');

class InstituteLocation {
  /**
   * Create a new institute location
   * @param {object} locationData - Location data
   * @returns {Promise<object>} - Created location
   */
  static async create(locationData) {
    const {
      instituteId,
      streetAddress,
      city,
      stateProvince,
      postalCode,
      country = 'Cameroon',
      latitude,
      longitude,
      landmark,
      directions
    } = locationData;

    try {
      const query = `
        INSERT INTO institute_locations (
          institute_id, street_address, city, state_province, postal_code,
          country, latitude, longitude, landmark, directions
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;
      
      const values = [
        instituteId, streetAddress, city, stateProvince, postalCode,
        country, latitude, longitude, landmark, directions
      ];
      
      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find location by institute ID
   * @param {number} instituteId - Institute ID
   * @returns {Promise<object|null>} - Location object or null
   */
  static async findByInstituteId(instituteId) {
    try {
      const query = `
        SELECT * FROM institute_locations
        WHERE institute_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `;
      
      const result = await db.query(query, [instituteId]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update location by institute ID
   * @param {number} instituteId - Institute ID
   * @param {object} updateData - Data to update
   * @returns {Promise<object|null>} - Updated location or null
   */
  static async updateByInstituteId(instituteId, updateData) {
    try {
      const allowedFields = [
        'street_address', 'city', 'state_province', 'postal_code',
        'country', 'latitude', 'longitude', 'landmark', 'directions'
      ];
      
      const updates = [];
      const values = [];
      let paramCount = 1;

      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key) && value !== undefined) {
          updates.push(`${key} = $${paramCount}`);
          values.push(value);
          paramCount++;
        }
      }

      if (updates.length === 0) {
        throw new Error('No valid fields to update');
      }

      values.push(instituteId);

      const query = `
        UPDATE institute_locations 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE institute_id = $${paramCount}
        RETURNING *
      `;

      const result = await db.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Verify address
   * @param {number} instituteId - Institute ID
   * @param {boolean} verified - Verification status
   * @returns {Promise<object|null>} - Updated location or null
   */
  static async verifyAddress(instituteId, verified = true) {
    try {
      const query = `
        UPDATE institute_locations 
        SET address_verified = $1, updated_at = CURRENT_TIMESTAMP
        WHERE institute_id = $2
        RETURNING *
      `;

      const result = await db.query(query, [verified, instituteId]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find institutes within radius
   * @param {number} latitude - Center latitude
   * @param {number} longitude - Center longitude
   * @param {number} radiusKm - Radius in kilometers
   * @returns {Promise<array>} - Array of institutes within radius
   */
  static async findWithinRadius(latitude, longitude, radiusKm = 10) {
    try {
      const query = `
        SELECT il.*, i.name, i.rating, i.review_count, i.services_offered,
               (
                 6371 * acos(
                   cos(radians($1)) * cos(radians(il.latitude)) *
                   cos(radians(il.longitude) - radians($2)) +
                   sin(radians($1)) * sin(radians(il.latitude))
                 )
               ) AS distance
        FROM institute_locations il
        JOIN institutes i ON il.institute_id = i.id
        WHERE i.is_active = true AND (i.status = 'verified' OR i.status = 'pending')
        AND il.latitude IS NOT NULL AND il.longitude IS NOT NULL
        HAVING distance <= $3
        ORDER BY distance ASC
      `;

      const result = await db.query(query, [latitude, longitude, radiusKm]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Geocode address (placeholder for external geocoding service)
   * @param {string} address - Full address string
   * @returns {Promise<object>} - Coordinates object
   */
  static async geocodeAddress(address) {
    // This would integrate with a geocoding service like Google Maps, OpenStreetMap, etc.
    // For now, return a placeholder implementation
    try {
      // In a real implementation, you would call an external geocoding API
      // Example: Google Maps Geocoding API, Mapbox Geocoding API, etc.
      
      // Placeholder coordinates for Cameroon cities
      const cityCoordinates = {
        'douala': { latitude: 4.0511, longitude: 9.7679 },
        'yaoundé': { latitude: 3.8480, longitude: 11.5021 },
        'yaounde': { latitude: 3.8480, longitude: 11.5021 },
        'bafoussam': { latitude: 5.4781, longitude: 10.4167 },
        'garoua': { latitude: 9.3265, longitude: 13.3958 },
        'bamenda': { latitude: 5.9597, longitude: 10.1453 }
      };

      const addressLower = address.toLowerCase();

      for (const [city, coords] of Object.entries(cityCoordinates)) {
        if (addressLower.includes(city)) {
          return {
            latitude: coords.latitude,
            longitude: coords.longitude,
            formatted_address: address
            // Note: Do NOT return city/country here - let the caller use their original values
          };
        }
      }

      // If no city match found, return null coordinates instead of defaulting to Douala
      // IMPORTANT: Do NOT return wrong coordinates - it's better to have no coordinates
      // than to have coordinates from a different country (which causes wrong distance calculations)
      return {
        latitude: null,
        longitude: null,
        formatted_address: address
        // Note: Returning null coordinates is better than returning wrong coordinates
      };
    } catch (error) {
      throw new Error('Geocoding failed: ' + error.message);
    }
  }

  /**
   * Validate address format
   * @param {object} addressData - Address data to validate
   * @returns {object} - Validation result
   */
  static validateAddress(addressData) {
    const errors = [];

    if (!addressData.streetAddress || addressData.streetAddress.trim().length < 5) {
      errors.push('Street address must be at least 5 characters long');
    }

    if (!addressData.city || addressData.city.trim().length < 2) {
      errors.push('City is required');
    }

    // Validate postal codes (flexible for international addresses)
    if (addressData.postalCode && !/^\d{4,10}$/.test(addressData.postalCode)) {
      errors.push('Postal code must be 4-10 digits');
    }

    // Validate coordinates if provided
    if (addressData.latitude !== undefined) {
      const lat = parseFloat(addressData.latitude);
      if (isNaN(lat) || lat < -90 || lat > 90) {
        errors.push('Latitude must be between -90 and 90');
      }
    }

    if (addressData.longitude !== undefined) {
      const lng = parseFloat(addressData.longitude);
      if (isNaN(lng) || lng < -180 || lng > 180) {
        errors.push('Longitude must be between -180 and 180');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Delete location by institute ID
   * @param {number} instituteId - Institute ID
   * @returns {Promise<boolean>} - True if deleted
   */
  static async deleteByInstituteId(instituteId) {
    try {
      const query = `
        DELETE FROM institute_locations
        WHERE institute_id = $1
        RETURNING id
      `;
      
      const result = await db.query(query, [instituteId]);
      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = InstituteLocation;
