const express = require('express');
const router = express.Router();
const { logError, logSimpleError, logCriticalError, logWarning, logInfo } = require('../utils/errorLogger');

/**
 * Test endpoint to verify error logging is working
 * @route GET /api/test-error-logging/throw-error
 */
router.get('/throw-error', (req, res, next) => {
  // This will be caught by the error handler middleware
  const error = new Error('Test error thrown intentionally');
  error.statusCode = 500;
  next(error);
});

/**
 * Test endpoint to manually log an error
 * @route GET /api/test-error-logging/manual-error
 */
router.get('/manual-error', async (req, res) => {
  try {
    await logSimpleError('This is a manually logged test error', 'error');
    res.json({
      success: true,
      message: 'Error logged manually to database'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to log error',
      error: error.message
    });
  }
});

/**
 * Test endpoint to log a critical error
 * @route GET /api/test-error-logging/critical-error
 */
router.get('/critical-error', async (req, res) => {
  try {
    await logCriticalError('This is a test critical error', {
      endpoint: '/api/test-error-logging/critical-error',
      method: 'GET'
    });
    res.json({
      success: true,
      message: 'Critical error logged to database'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to log critical error',
      error: error.message
    });
  }
});

/**
 * Test endpoint to log a warning
 * @route GET /api/test-error-logging/warning
 */
router.get('/warning', async (req, res) => {
  try {
    await logWarning('This is a test warning message', {
      endpoint: '/api/test-error-logging/warning',
      method: 'GET'
    });
    res.json({
      success: true,
      message: 'Warning logged to database'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to log warning',
      error: error.message
    });
  }
});

/**
 * Test endpoint to log an info message
 * @route GET /api/test-error-logging/info
 */
router.get('/info', async (req, res) => {
  try {
    await logInfo('This is a test info message', {
      endpoint: '/api/test-error-logging/info',
      method: 'GET'
    });
    res.json({
      success: true,
      message: 'Info logged to database'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to log info',
      error: error.message
    });
  }
});

/**
 * Test endpoint to log multiple errors at once
 * @route GET /api/test-error-logging/bulk-test
 */
router.get('/bulk-test', async (req, res) => {
  try {
    // Log different types of errors
    await logInfo('Bulk test started');
    await logWarning('This is a warning from bulk test');
    await logSimpleError('This is an error from bulk test');
    await logCriticalError('This is a critical error from bulk test');

    res.json({
      success: true,
      message: '4 test logs created (info, warning, error, critical)'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create bulk test logs',
      error: error.message
    });
  }
});

/**
 * Test endpoint to simulate a database error
 * @route GET /api/test-error-logging/database-error
 */
router.get('/database-error', async (req, res, next) => {
  try {
    const db = require('../config/database');
    // This will cause a database error (invalid SQL)
    await db.query('SELECT * FROM non_existent_table');
    res.json({ success: true });
  } catch (error) {
    // Pass to error handler middleware
    next(error);
  }
});

/**
 * Test endpoint to simulate a validation error
 * @route GET /api/test-error-logging/validation-error
 */
router.get('/validation-error', (req, res, next) => {
  const error = new Error('Validation failed: Email is required');
  error.name = 'ValidationError';
  error.errors = {
    email: 'Email is required',
    password: 'Password must be at least 8 characters'
  };
  next(error);
});

module.exports = router;

