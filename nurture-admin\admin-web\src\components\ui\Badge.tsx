import React from 'react'
import { cn } from '../../lib/utils'

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md'
  children: React.ReactNode
}

const badgeVariants = {
  default: 'bg-gray-100 text-gray-800',
  success: 'bg-green-100 text-green-800',
  warning: 'bg-yellow-100 text-yellow-800',
  error: 'bg-red-100 text-red-800',
  info: 'bg-blue-100 text-blue-800',
}

const badgeSizes = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-2.5 py-1.5 text-xs',
}

export function Badge({ 
  variant = 'default', 
  size = 'md', 
  className, 
  children, 
  ...props 
}: BadgeProps) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full font-medium',
        badgeVariants[variant],
        badgeSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </span>
  )
}

// Status-specific badge components
export function StatusBadge({ status }: { status: string }) {
  // Handle undefined or null status
  if (!status) {
    return (
      <Badge variant="default">
        UNKNOWN
      </Badge>
    )
  }

  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'verified':
      case 'approved':
      case 'active':
        return 'success'
      case 'pending':
      case 'under_review':
        return 'warning'
      case 'rejected':
      case 'suspended':
      case 'inactive':
      case 'deleted':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Badge variant={getVariant(status)}>
      {status.replace('_', ' ').toUpperCase()}
    </Badge>
  )
}
