import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { adminApi } from '../lib/api'
import { useAuthStore } from '../stores/authStore'

export function PrivacyPolicy() {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [policy, setPolicy] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState('')
  const [editedTitle, setEditedTitle] = useState('')
  const [saving, setSaving] = useState(false)

  const isSuperAdmin = user?.role === 'super_admin'

  useEffect(() => {
    fetchPolicy()
  }, [])

  const fetchPolicy = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await adminApi.getPrivacyPolicy()
      if (response.success && response.data?.policies && response.data.policies.length > 0) {
        const activePolicy = response.data.policies[0]
        setPolicy(activePolicy)
        setEditedContent(activePolicy.content)
        setEditedTitle(activePolicy.title)
      } else {
        setError('No privacy policy found')
      }
    } catch (err: any) {
      console.error('Failed to fetch privacy policy:', err)
      setError(err.response?.data?.message || 'Failed to fetch privacy policy')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
    if (policy) {
      setEditedContent(policy.content)
      setEditedTitle(policy.title)
    }
  }

  const handleSave = async () => {
    if (!policy) return

    try {
      setSaving(true)
      const response = await adminApi.updatePrivacyPolicy(policy.id, {
        title: editedTitle,
        content: editedContent
      })

      if (response.success) {
        setPolicy(response.data)
        setIsEditing(false)
        alert('Privacy policy updated successfully')
      }
    } catch (err: any) {
      console.error('Failed to update privacy policy:', err)
      alert(err.response?.data?.message || 'Failed to update privacy policy')
    } finally {
      setSaving(false)
    }
  }

  const handleViewPublic = () => {
    if (policy) {
      const publicUrl = `${window.location.origin}/api/privacy-policy/${policy.url_slug}`
      window.open(publicUrl, '_blank')
    }
  }

  if (loading) {
    return (
      <div className="space-y-9">
        <div className="animate-slide-in">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 tracking-tight">Privacy Policy</h1>
          <p className="text-base text-gray-600 mt-3 max-w-2xl">
            Loading privacy policy...
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-9">
        <div className="animate-slide-in">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 tracking-tight">Privacy Policy</h1>
          <p className="text-base text-red-600 mt-3 max-w-2xl">
            {error}
          </p>
          {isSuperAdmin && (
            <Button onClick={() => navigate('/admin/dashboard')} className="mt-4">
              Back to Dashboard
            </Button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-7">
      {/* Page Header */}
      <div className="animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Privacy Policy & Terms</h1>
            <p className="text-sm text-gray-600 mt-1">
              {isEditing ? 'Edit privacy policy and terms of service' : 'View privacy policy and terms of service'}
            </p>
          </div>
          <div className="flex space-x-2">
            {!isEditing && (
              <>
                <Button
                  onClick={handleViewPublic}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  <span>View Public</span>
                </Button>
                {isSuperAdmin && (
                  <Button
                    onClick={handleEdit}
                    className="flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>Edit</span>
                  </Button>
                )}
              </>
            )}
            {isEditing && (
              <>
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving}
                  className="flex items-center space-x-2"
                >
                  {saving ? (
                    <>
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Save</span>
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Policy Content */}
      <Card variant="elevated">
        <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100 border-b border-primary-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-primary rounded-md flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              {isEditing ? (
                <input
                  type="text"
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  className="text-sm font-bold text-gray-900 bg-white border border-gray-300 rounded px-2 py-1"
                />
              ) : (
                <h3 className="text-sm font-bold text-gray-900">{policy?.title}</h3>
              )}
            </div>
            {policy && (
              <div className="text-xs text-gray-600">
                Version {policy.version} • Last updated: {new Date(policy.updated_at).toLocaleDateString()}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {isEditing ? (
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm"
              placeholder="Enter privacy policy content (HTML supported)"
            />
          ) : (
            <div 
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: policy?.content || '' }}
            />
          )}
        </CardContent>
      </Card>

      {/* Policy Info */}
      {policy && !isEditing && (
        <Card variant="elevated">
          <CardHeader className="bg-gradient-to-r from-secondary-50 to-secondary-100 border-b border-secondary-200">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-secondary rounded-md flex items-center justify-center">
                <svg className="w-3 h-3 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-sm font-bold text-gray-900">Policy Information</h3>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs font-semibold text-gray-500 uppercase">URL Slug</p>
                <p className="text-sm text-gray-900 mt-1">{policy.url_slug}</p>
              </div>
              <div>
                <p className="text-xs font-semibold text-gray-500 uppercase">Status</p>
                <p className="text-sm text-gray-900 mt-1">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    policy.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {policy.is_active ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-xs font-semibold text-gray-500 uppercase">Created At</p>
                <p className="text-sm text-gray-900 mt-1">{new Date(policy.created_at).toLocaleString()}</p>
              </div>
              <div>
                <p className="text-xs font-semibold text-gray-500 uppercase">Public URL</p>
                <p className="text-sm text-primary mt-1 break-all">
                  /api/privacy-policy/{policy.url_slug}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

