import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal } from 'react-native';
import { useTranslation } from '@/lib/i18n';

interface DateOfBirthPickerProps {
  selectedDate?: string;
  onSelect: (date: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
}

export default function DateOfBirthPicker({
  selectedDate,
  onSelect,
  placeholder,
  label,
  error,
}: DateOfBirthPickerProps) {
  const { t } = useTranslation();
  const [showPicker, setShowPicker] = useState(false);

  // Parse the selected date or use today's date
  const getInitialDate = () => {
    if (selectedDate) {
      // Parse date string in YYYY-MM-DD format without timezone conversion
      const [year, month, day] = selectedDate.split('-').map(Number);
      if (year && month && day) {
        return new Date(year, month - 1, day);
      }
    }
    // Default to 18 years ago
    const date = new Date();
    date.setFullYear(date.getFullYear() - 18);
    return date;
  };

  const initialDate = getInitialDate();
  const [date, setDate] = useState<Date>(initialDate);
  const [selectedDay, setSelectedDay] = useState<number>(initialDate.getDate());
  const [selectedMonth, setSelectedMonth] = useState<number>(initialDate.getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(initialDate.getFullYear());

  // Update state when selectedDate prop changes
  useEffect(() => {
    if (selectedDate) {
      const [year, month, day] = selectedDate.split('-').map(Number);
      if (year && month && day) {
        const newDate = new Date(year, month - 1, day);
        setDate(newDate);
        setSelectedDay(day);
        setSelectedMonth(month - 1);
        setSelectedYear(year);
      }
    }
  }, [selectedDate]);

  // Generate years array (from 1900 to current year)
  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const yearArray = [];
    for (let i = currentYear; i >= 1900; i--) {
      yearArray.push(i);
    }
    return yearArray;
  }, []);

  // Generate months array
  const months = useMemo(() => {
    return [
      t('common.january'),
      t('common.february'),
      t('common.march'),
      t('common.april'),
      t('common.may'),
      t('common.june'),
      t('common.july'),
      t('common.august'),
      t('common.september'),
      t('common.october'),
      t('common.november'),
      t('common.december'),
    ];
  }, [t]);

  // Generate days array based on selected month and year
  const days = useMemo(() => {
    const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
    const dayArray = [];
    for (let i = 1; i <= daysInMonth; i++) {
      dayArray.push(i);
    }
    return dayArray;
  }, [selectedMonth, selectedYear]);

  // Get day name
  const getDayName = (day: number, month: number, year: number) => {
    const date = new Date(year, month, day);
    const dayNames = [
      t('common.sunday'),
      t('common.monday'),
      t('common.tuesday'),
      t('common.wednesday'),
      t('common.thursday'),
      t('common.friday'),
      t('common.saturday'),
    ];
    return dayNames[date.getDay()];
  };

  const handleConfirm = () => {
    // Create date string in YYYY-MM-DD format without timezone conversion
    const year = selectedYear.toString().padStart(4, '0');
    const month = (selectedMonth + 1).toString().padStart(2, '0'); // Month is 0-indexed
    const day = selectedDay.toString().padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    onSelect(formattedDate);
    setShowPicker(false);
  };

  const formatDisplayDate = (dateString?: string): string => {
    if (!dateString) return placeholder || t('auth.selectDateOfBirth');

    try {
      // Parse date string in YYYY-MM-DD format without timezone conversion
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return placeholder || t('auth.selectDateOfBirth');
    }
  };

  const displayText = formatDisplayDate(selectedDate);
  const isPlaceholder = !selectedDate;

  return (
    <View>
      {label && (
        <Text className="text-sm font-medium text-gray-700 mb-2">
          {label} *
        </Text>
      )}

      <TouchableOpacity
        onPress={() => setShowPicker(true)}
        className={`flex-row border rounded-lg px-3 py-3 items-center justify-between ${
          error ? 'border-red-300' : 'border-gray-300'
        } bg-white`}
        style={{
          height: 50,
        }}
      >
        <Text className={`text-base flex-1 ${
          isPlaceholder ? 'text-gray-400' : 'text-gray-900'
        }`}>
          {displayText}
        </Text>
        <Text className="text-gray-400 text-xs">📅</Text>
      </TouchableOpacity>

      {error && (
        <Text className="text-red-500 text-sm mt-1 px-1">
          {error}
        </Text>
      )}

      <Modal
        visible={showPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPicker(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View className="bg-white rounded-t-3xl p-6 pb-8">
            {/* Header */}
            <View className="mb-6">
              <Text className="text-xl font-bold text-gray-900 text-center">
                {t('auth.selectDateOfBirth')}
              </Text>
              <Text className="text-sm text-gray-600 text-center mt-2">
                {getDayName(selectedDay, selectedMonth, selectedYear)}, {selectedDay} {months[selectedMonth]} {selectedYear}
              </Text>
            </View>

            {/* Date Selectors */}
            <View className="flex-row justify-between gap-4 mb-6">
              {/* Day Selector */}
              <View className="flex-1">
                <Text className="text-xs font-semibold text-gray-600 mb-2 text-center">
                  {t('common.day')}
                </Text>
                <ScrollView
                  style={{ height: 150 }}
                  showsVerticalScrollIndicator={false}
                  snapToInterval={40}
                  decelerationRate="fast"
                >
                  {days.map((d) => (
                    <TouchableOpacity
                      key={d}
                      onPress={() => setSelectedDay(d)}
                      className={`py-2 px-3 rounded-lg mb-1 ${
                        selectedDay === d
                          ? 'bg-blue-500'
                          : 'bg-gray-100'
                      }`}
                    >
                      <Text className={`text-center font-semibold ${
                        selectedDay === d
                          ? 'text-white'
                          : 'text-gray-700'
                      }`}>
                        {d}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Month Selector */}
              <View className="flex-1">
                <Text className="text-xs font-semibold text-gray-600 mb-2 text-center">
                  {t('common.month')}
                </Text>
                <ScrollView
                  style={{ height: 150 }}
                  showsVerticalScrollIndicator={false}
                  snapToInterval={40}
                  decelerationRate="fast"
                >
                  {months.map((m, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => setSelectedMonth(index)}
                      className={`py-2 px-3 rounded-lg mb-1 ${
                        selectedMonth === index
                          ? 'bg-blue-500'
                          : 'bg-gray-100'
                      }`}
                    >
                      <Text className={`text-center font-semibold text-xs ${
                        selectedMonth === index
                          ? 'text-white'
                          : 'text-gray-700'
                      }`}>
                        {m}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Year Selector */}
              <View className="flex-1">
                <Text className="text-xs font-semibold text-gray-600 mb-2 text-center">
                  {t('common.year')}
                </Text>
                <ScrollView
                  style={{ height: 150 }}
                  showsVerticalScrollIndicator={false}
                  snapToInterval={40}
                  decelerationRate="fast"
                >
                  {years.map((y) => (
                    <TouchableOpacity
                      key={y}
                      onPress={() => setSelectedYear(y)}
                      className={`py-2 px-3 rounded-lg mb-1 ${
                        selectedYear === y
                          ? 'bg-blue-500'
                          : 'bg-gray-100'
                      }`}
                    >
                      <Text className={`text-center font-semibold ${
                        selectedYear === y
                          ? 'text-white'
                          : 'text-gray-700'
                      }`}>
                        {y}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>

            {/* Buttons */}
            <View className="flex-row gap-3 mt-6">
              <TouchableOpacity
                onPress={() => setShowPicker(false)}
                className="flex-1 py-3 bg-gray-200 rounded-lg"
              >
                <Text className="text-center font-semibold text-gray-700">
                  {t('common.cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleConfirm}
                className="flex-1 py-3 bg-blue-600 rounded-lg"
              >
                <Text className="text-center font-semibold text-white">
                  {t('common.ok')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

