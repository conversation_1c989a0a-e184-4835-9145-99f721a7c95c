-- =====================================================
-- DEFAULT SERVICES SEED DATA
-- =====================================================
-- This script creates default service categories and services
-- for the Nurture wellness application
-- =====================================================

-- Insert default services (without institute_id for template services)
-- These can be used as templates for institutes to copy from

-- Massage Services
INSERT INTO services (
    institute_id, name, description, category, subcategory, 
    price, currency, duration_minutes, requirements, benefits, 
    contraindications, is_active, requires_appointment, 
    is_online_available, is_home_service_available, created_at, updated_at
) VALUES 
(
    NULL, -- Template service
    'Deep Tissue Massage',
    'Therapeutic massage targeting deeper layers of muscle and connective tissue to relieve chronic tension and pain.',
    'Massage',
    'Therapeutic',
    25000.00,
    'XAF',
    60,
    'No recent injuries or surgeries',
    'Reduces muscle tension, improves circulation, relieves chronic pain',
    'Recent surgery, severe osteoporosis, blood clots',
    true,
    true,
    false,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Swedish Massage',
    'Gentle, relaxing full-body massage using long strokes and kneading techniques.',
    'Massage',
    'Relaxation',
    20000.00,
    'XAF',
    60,
    'None',
    'Promotes relaxation, improves circulation, reduces stress',
    'Severe heart conditions, recent surgery',
    true,
    true,
    false,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Hot Stone Massage',
    'Relaxing massage using heated stones to warm and relax muscles.',
    'Massage',
    'Therapeutic',
    30000.00,
    'XAF',
    75,
    'No heat sensitivity',
    'Deep muscle relaxation, improved circulation, stress relief',
    'Pregnancy, diabetes, heart conditions, heat sensitivity',
    true,
    true,
    false,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Aromatherapy Massage',
    'Therapeutic massage combined with essential oils for enhanced relaxation and healing.',
    'Massage',
    'Aromatherapy',
    22000.00,
    'XAF',
    60,
    'No allergies to essential oils',
    'Stress relief, improved mood, muscle relaxation',
    'Pregnancy (certain oils), allergies to essential oils',
    true,
    true,
    false,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- Physiotherapy Services
(
    NULL,
    'Physical Assessment',
    'Comprehensive evaluation of movement, strength, and physical function.',
    'Physiotherapy',
    'Assessment',
    15000.00,
    'XAF',
    45,
    'Medical referral preferred',
    'Identifies movement issues, creates treatment plan',
    'None',
    true,
    true,
    true,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Manual Therapy',
    'Hands-on treatment to improve joint mobility and reduce pain.',
    'Physiotherapy',
    'Treatment',
    18000.00,
    'XAF',
    30,
    'Physical assessment completed',
    'Improved mobility, pain reduction, better function',
    'Acute injuries, fractures, severe osteoporosis',
    true,
    true,
    false,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Exercise Therapy',
    'Customized exercise program to improve strength, flexibility, and function.',
    'Physiotherapy',
    'Rehabilitation',
    12000.00,
    'XAF',
    45,
    'Physical assessment completed',
    'Improved strength, better movement patterns, injury prevention',
    'Acute pain, unstable conditions',
    true,
    true,
    true,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- Wellness Services
(
    NULL,
    'Yoga Session',
    'Individual or group yoga instruction focusing on postures, breathing, and mindfulness.',
    'Wellness',
    'Yoga',
    12000.00,
    'XAF',
    60,
    'Basic fitness level',
    'Improved flexibility, stress reduction, better balance',
    'Recent injuries, severe back problems',
    true,
    true,
    true,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Meditation Class',
    'Guided meditation session for stress reduction and mental clarity.',
    'Wellness',
    'Mindfulness',
    8000.00,
    'XAF',
    30,
    'None',
    'Stress reduction, improved focus, emotional balance',
    'Severe mental health conditions (consult doctor)',
    true,
    true,
    true,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Nutrition Consultation',
    'Personalized nutrition assessment and dietary planning.',
    'Wellness',
    'Nutrition',
    20000.00,
    'XAF',
    60,
    'Health history questionnaire',
    'Improved nutrition, weight management, better health',
    'Eating disorders (requires specialized care)',
    true,
    true,
    true,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- Beauty & Spa Services
(
    NULL,
    'Facial Treatment',
    'Deep cleansing and rejuvenating facial treatment for healthy skin.',
    'Beauty',
    'Skincare',
    18000.00,
    'XAF',
    60,
    'No active skin infections',
    'Cleaner skin, improved texture, relaxation',
    'Active acne, recent chemical peels, allergies',
    true,
    true,
    false,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    NULL,
    'Body Scrub',
    'Exfoliating treatment to remove dead skin cells and improve skin texture.',
    'Beauty',
    'Body Treatment',
    15000.00,
    'XAF',
    45,
    'No open wounds or cuts',
    'Smoother skin, improved circulation, relaxation',
    'Sunburn, recent waxing, sensitive skin conditions',
    true,
    true,
    false,
    false,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)

ON CONFLICT DO NOTHING;

-- =====================================================
-- SERVICES SEED DATA COMPLETION
-- =====================================================
-- Total template services created: 12
-- Categories: Massage (4), Physiotherapy (3), Wellness (3), Beauty (2)
-- These are template services that institutes can copy and customize
-- =====================================================
