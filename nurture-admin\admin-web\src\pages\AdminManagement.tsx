import React, { useState, useEffect } from 'react'
import { useAuthStore } from '../stores/authStore'
import { Card, CardContent } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { Input } from '../components/ui/Input'
import { adminApi } from '../lib/api'

interface Admin {
  id: number
  email: string
  first_name: string
  last_name: string
  role: 'super_admin' | 'admin' | 'moderator'
  is_active: boolean
  created_at: string
  updated_at: string
  last_login?: string
}

interface Message {
  type: 'success' | 'error'
  text: string
}

interface CreateAdminForm {
  firstName: string
  lastName: string
  email: string
  password: string
  role: 'super_admin' | 'admin' | 'moderator'
}

export function AdminManagement() {
  const { user } = useAuthStore()
  const [admins, setAdmins] = useState<Admin[]>([])
  const [filteredAdmins, setFilteredAdmins] = useState<Admin[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [message, setMessage] = useState<Message | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [createForm, setCreateForm] = useState<CreateAdminForm>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: 'admin'
  })
  const [createLoading, setCreateLoading] = useState(false)

  // Check if current user is super admin
  const isSuperAdmin = user?.role === 'super_admin'

  useEffect(() => {
    if (user) {
      loadAdmins()
    }
  }, [user])

  useEffect(() => {
    filterAdmins()
  }, [admins, searchTerm, roleFilter])

  const loadAdmins = async () => {
    try {
      setIsLoading(true)
      const response = await adminApi.getAdmins()
      if (response.success && response.data) {
        setAdmins(response.data.admins || [])
      }
    } catch (error) {
      console.error('Failed to load admins:', error)
      setMessage({ type: 'error', text: 'Failed to load admin users' })
    } finally {
      setIsLoading(false)
    }
  }

  const filterAdmins = () => {
    let filtered = admins.filter(admin => {
      const matchesSearch =
        admin.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        admin.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        admin.email.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesRole = roleFilter === 'all' || admin.role === roleFilter

      return matchesSearch && matchesRole
    })

    setFilteredAdmins(filtered)
  }

  const handleToggleStatus = async (adminId: number, currentStatus: boolean) => {
    if (adminId === Number(user?.id)) {
      setMessage({ type: 'error', text: 'You cannot deactivate your own account' })
      return
    }

    try {
      const response = await adminApi.toggleAdminStatus(adminId, !currentStatus)
      if (response.success) {
        setMessage({
          type: 'success',
          text: `Admin ${!currentStatus ? 'activated' : 'deactivated'} successfully`
        })
        loadAdmins()
        setTimeout(() => setMessage(null), 3000)
      } else {
        setMessage({ type: 'error', text: response.message || 'Failed to update admin status' })
      }
    } catch (error) {
      console.error('Toggle admin status error:', error)
      setMessage({ type: 'error', text: 'Failed to update admin status' })
    }
  }

  const handleDeleteAdmin = async (adminId: number, adminName: string) => {
    if (adminId === Number(user?.id)) {
      setMessage({ type: 'error', text: 'You cannot delete your own account' })
      return
    }

    if (!confirm(`Are you sure you want to delete ${adminName}? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await adminApi.deleteAdmin(adminId.toString())
      if (response.success) {
        setMessage({ type: 'success', text: 'Admin deleted successfully' })
        loadAdmins()
        setTimeout(() => setMessage(null), 3000)
      } else {
        setMessage({ type: 'error', text: response.message || 'Failed to delete admin' })
      }
    } catch (error) {
      console.error('Delete admin error:', error)
      setMessage({ type: 'error', text: 'Failed to delete admin' })
    }
  }

  const handleCreateAdmin = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setCreateLoading(true)
      const response = await adminApi.createAdmin({
        firstName: createForm.firstName.trim(),
        lastName: createForm.lastName.trim(),
        email: createForm.email.trim(),
        password: createForm.password,
        role: createForm.role
      })

      if (response.success) {
        setMessage({ type: 'success', text: 'Admin created successfully' })
        setShowCreateModal(false)
        setCreateForm({
          firstName: '',
          lastName: '',
          email: '',
          password: '',
          role: 'admin'
        })
        loadAdmins()
        setTimeout(() => setMessage(null), 3000)
      } else {
        setMessage({ type: 'error', text: response.message || 'Failed to create admin' })
      }
    } catch (error: any) {
      console.error('Create admin error:', error)
      setMessage({ type: 'error', text: error.response?.data?.message || 'Failed to create admin' })
    } finally {
      setCreateLoading(false)
    }
  }

  const formatRole = (role: string) => {
    return role.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-purple-100 text-purple-800'
      case 'admin':
        return 'bg-blue-100 text-blue-800'
      case 'moderator':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!user) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Please login to access admin management.</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-slide-in">
          <h1 className="text-xl font-bold text-gray-900">Admins</h1>
          <p className="text-sm text-gray-600 mt-1">Loading admin users...</p>
        </div>
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {message && (
        <div className="animate-slide-in">
          <div className={`p-4 rounded-lg border ${
            message.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          } flex items-center space-x-3`}>
            {message.type === 'success' ? (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* Page Header */}
      <div className="animate-slide-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Admins</h1>
            <p className="text-sm text-gray-600 mt-1">
              {isSuperAdmin ? 'Manage admin users and their permissions' : 'View admin users in the system'}
            </p>
          </div>
          {isSuperAdmin && (
            <Button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>Add Admin</span>
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="sm:w-48">
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="all">All Roles</option>
                <option value="super_admin">Super Admin</option>
                <option value="admin">Admin</option>
                <option value="moderator">Moderator</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Admin Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAdmins.map((admin) => (
          <Card key={admin.id} variant="elevated" className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {admin.first_name.charAt(0)}{admin.last_name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-base font-semibold text-gray-900">
                      {admin.first_name} {admin.last_name}
                    </h3>
                    <p className="text-sm text-gray-600">{admin.email}</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mb-4">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRoleBadgeColor(admin.role)}`}>
                  {formatRole(admin.role)}
                </span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  admin.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {admin.is_active ? 'Active' : 'Inactive'}
                </span>
                {admin.id === Number(user?.id) && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    You
                  </span>
                )}
              </div>

              {admin.last_login && (
                <p className="text-xs text-gray-500 mb-4">
                  Last login: {new Date(admin.last_login).toLocaleDateString()}
                </p>
              )}

              <div className="flex space-x-2">
                {isSuperAdmin && admin.id !== Number(user?.id) && (
                  <>
                    <Button
                      onClick={() => handleToggleStatus(admin.id, admin.is_active)}
                      variant={admin.is_active ? "secondary" : "primary"}
                      size="sm"
                      className="flex-1"
                    >
                      {admin.is_active ? 'Deactivate' : 'Activate'}
                    </Button>
                    <Button
                      onClick={() => handleDeleteAdmin(admin.id, `${admin.first_name} ${admin.last_name}`)}
                      variant="danger"
                      size="sm"
                      className="flex-1"
                    >
                      Delete
                    </Button>
                  </>
                )}
                {isSuperAdmin && admin.id === Number(user?.id) && (
                  <div className="flex-1 text-center text-sm text-gray-500 py-2">
                    Cannot modify your own account
                  </div>
                )}
                {!isSuperAdmin && (
                  <div className="flex-1 text-center text-sm text-gray-500 py-2">
                    View only - Contact Super Admin for changes
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAdmins.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No admins found</h3>
            <p className="text-gray-600">
              {searchTerm || roleFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'No admin users have been created yet.'}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Create Admin Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Create New Admin</h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleCreateAdmin} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="First Name"
                    type="text"
                    value={createForm.firstName}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, firstName: e.target.value }))}
                    required
                    placeholder="Enter first name"
                  />
                  <Input
                    label="Last Name"
                    type="text"
                    value={createForm.lastName}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, lastName: e.target.value }))}
                    required
                    placeholder="Enter last name"
                  />
                </div>

                <Input
                  label="Email"
                  type="email"
                  value={createForm.email}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, email: e.target.value }))}
                  required
                  placeholder="Enter email address"
                />

                <Input
                  label="Password"
                  type="password"
                  value={createForm.password}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, password: e.target.value }))}
                  required
                  placeholder="Enter password"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                  <select
                    value={createForm.role}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, role: e.target.value as 'super_admin' | 'admin' | 'moderator' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  >
                    <option value="super_admin">Super Admin</option>
                    <option value="admin">Admin</option>
                    <option value="moderator">Moderator</option>
                  </select>

                  {/* Role descriptions */}
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Role Permissions:</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      {createForm.role === 'super_admin' && (
                        <div>
                          <strong>Super Admin:</strong> Full system access - can manage all admins, institutes, users, and system settings
                        </div>
                      )}
                      {createForm.role === 'admin' && (
                        <div>
                          <strong>Admin:</strong> Can manage institutes and users, view admin list (cannot manage other admins)
                        </div>
                      )}
                      {createForm.role === 'moderator' && (
                        <div>
                          <strong>Moderator:</strong> Limited access - can view and moderate content, basic user management
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => setShowCreateModal(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createLoading}
                    className="flex-1"
                  >
                    {createLoading ? <LoadingSpinner size="sm" /> : 'Create Admin'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
