# Database Seeding

This folder contains seed data for the Nurture application database. The seeding system provides default data for development, testing, and initial production setup.

## 📁 Seed Files

### 1. `01_admin_users.sql`
Creates default admin users with different roles:
- **Super Admin** (`<EMAIL>`) - Full system access
- **Moderator** (`<EMAIL>`) - Limited administrative access
- **Content Manager** (`<EMAIL>`) - Content and service management

### 2. `02_default_services.sql`
Creates template services that institutes can use:
- **Massage Services**: Deep Tissue, Swedish, Hot Stone, Aromatherapy
- **Physiotherapy**: Assessment, Manual Therapy, Exercise Therapy
- **Wellness**: Yoga, Meditation, Nutrition Consultation
- **Beauty**: Facial Treatment, Body Scrub

### 3. `03_privacy_policies.sql`
Creates default privacy policies and terms of service:
- **English Version** (`privacy-policy`) - Active by default
- **French Version** (`politique-de-confidentialite`) - Inactive by default
- Includes comprehensive privacy policy and terms of servic

## 🚀 Usage Commands

### Execute Seeding
```bash
# Seed the database with default data
npm run db:seed
```

### Individual Files
```bash
# Execute specific seed file
npm run db:execute database/seed/01_admin_users.sql
npm run db:execute database/seed/02_default_services.sql
npm run db:execute database/seed/03_privacy_policies.sql

# Dry run specific file
npm run db:execute database/seed/01_admin_users.sql --dry-run
```

## 🔑 Default Credentials

All seeded admin accounts use the same default password: **`admin123`**

### Admin Accounts
- `<EMAIL>` - Super Admin (all permissions)
- `<EMAIL>` - Moderator (limited permissions)
- `<EMAIL>` - Content Manager (content permissions)

## 🛡️ Safety Features

- **Graceful Conflicts**: Uses `ON CONFLICT DO UPDATE` for safe re-seeding
- **Transaction Support**: All operations in transactions with rollback
- **Dry Run Mode**: Validate before executing
- **Error Handling**: Continues on conflicts, reports all issues
- **Progress Tracking**: Real-time progress for large operations

## 📊 Seeded Data Summary

After successful seeding, you'll have:

- ✅ **3 Admin Users** with different permission levels
- ✅ **12 Template Services** across 4 categories (Massage, Physiotherapy, Wellness, Beauty)
- ✅ **2 Privacy Policies** in English and French with comprehensive terms

## 🔄 Re-seeding

The seed scripts are designed to be run multiple times safely:

1. **Admin users**: Updates existing accounts with latest data
2. **Template services**: Updates template services, adds new ones

## 🧪 Testing

Use the seeded data for:
- **Development**: Admin accounts and template services for local development
- **Testing**: Consistent admin and service data across environments
- **Production Setup**: Initial admin accounts and service templates
- **Training**: Sample data for user training

## 📝 Customization

To customize seed data:

1. **Edit existing files**: Modify the SQL files directly
2. **Add new files**: Create new numbered files (e.g., `05_custom_data.sql`)
3. **Update script**: Add new files to `seedFiles` array in `scripts/seed-database.js`

## ⚠️ Important Notes

- **Production Use**: Review and customize data before production seeding
- **Password Security**: Change default passwords in production
- **Data Conflicts**: Existing data with same emails will be updated
- **Backup First**: Always backup production data before seeding

## 🔧 Troubleshooting

### Common Issues

1. **Connection Errors**: Check database configuration in `.env`
2. **Permission Errors**: Ensure database user has INSERT/UPDATE permissions
3. **Constraint Violations**: Check for existing data conflicts
4. **File Not Found**: Ensure all seed files exist in the correct location

### Getting Help

Check individual files:
```bash
npm run db:execute database/seed/01_admin_users.sql --dry-run
```
