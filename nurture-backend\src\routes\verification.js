const express = require('express');
const router = express.Router();

// Import controllers
const {
  upload,
  submitVerification,
  getVerificationStatus,
  getPendingVerifications,
  reviewVerification,
  getVerificationStatistics,
  getVerificationDocument
} = require('../controllers/verificationController');

// Import middleware
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const {
  validateVerificationSubmission,
  validateVerificationReview
} = require('../middleware/validation');

/**
 * @route   POST /api/verification/submit
 * @desc    Submit verification document for an institute
 * @access  Private (Institute owner only)
 */
router.post('/submit',
  authenticateToken,
  upload.single('document'),
  validateVerificationSubmission,
  submitVerification
);

/**
 * @route   GET /api/verification/status/:instituteId
 * @desc    Get verification status for an institute
 * @access  Private (Institute owner only)
 */
router.get('/status/:instituteId',
  authenticateToken,
  getVerificationStatus
);

/**
 * @route   GET /api/verification/document/:id
 * @desc    Get verification document by verification ID
 * @access  Private (Institute owner or admin)
 */
router.get('/document/:id',
  authenticateToken,
  getVerificationDocument
);

/**
 * @route   GET /api/verification/pending
 * @desc    Get pending verifications for review
 * @access  Private (Admin only)
 */
router.get('/pending',
  authenticateToken,
  requireAdmin,
  getPendingVerifications
);

/**
 * @route   POST /api/verification/review/:id
 * @desc    Review a verification (approve/reject/under_review)
 * @access  Private (Admin only)
 */
router.post('/review/:id',
  authenticateToken,
  requireAdmin,
  validateVerificationReview,
  reviewVerification
);

/**
 * @route   GET /api/verification/statistics
 * @desc    Get verification statistics
 * @access  Private (Admin only)
 */
router.get('/statistics',
  authenticateToken,
  requireAdmin,
  getVerificationStatistics
);

module.exports = router;
