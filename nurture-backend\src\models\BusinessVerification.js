const db = require('../config/database');

class BusinessVerification {
  /**
   * Create a new business verification record
   * @param {object} verificationData - Verification data
   * @returns {Promise<object>} - Created verification record
   */
  static async create(verificationData) {
    const {
      instituteId,
      verificationType,
      documentType,
      documentNumber,
      documentUrl,
      documentExpiryDate
    } = verificationData;

    try {
      const query = `
        INSERT INTO business_verification (
          institute_id, verification_type, document_type, document_number,
          document_url, document_expiry_date
        )
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `;
      
      const values = [
        instituteId, verificationType, documentType, documentNumber,
        documentUrl, documentExpiryDate
      ];
      
      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find verification records by institute ID
   * @param {number} instituteId - Institute ID
   * @returns {Promise<array>} - Array of verification records
   */
  static async findByInstituteId(instituteId) {
    try {
      const query = `
        SELECT bv.*, u.first_name, u.last_name
        FROM business_verification bv
        LEFT JOIN users u ON bv.reviewed_by = u.id
        WHERE bv.institute_id = $1
        ORDER BY bv.created_at DESC
      `;
      
      const result = await db.query(query, [instituteId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find verification record by ID
   * @param {number} id - Verification ID
   * @returns {Promise<object|null>} - Verification record or null
   */
  static async findById(id) {
    try {
      const query = `
        SELECT bv.*, u.first_name, u.last_name, i.name as institute_name
        FROM business_verification bv
        LEFT JOIN users u ON bv.reviewed_by = u.id
        LEFT JOIN institutes i ON bv.institute_id = i.id
        WHERE bv.id = $1
      `;
      
      const result = await db.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get pending verifications for review
   * @param {number} limit - Number of records to return
   * @param {number} offset - Offset for pagination
   * @returns {Promise<array>} - Array of pending verifications
   */
  static async getPendingVerifications(limit = 50, offset = 0) {
    try {
      const query = `
        SELECT bv.*, i.name as institute_name, i.email as institute_email,
               u.first_name, u.last_name, u.email as owner_email
        FROM business_verification bv
        JOIN institutes i ON bv.institute_id = i.id
        JOIN users u ON i.owner_id = u.id
        WHERE bv.status IN ('pending', 'under_review')
        ORDER BY bv.submitted_at ASC
        LIMIT $1 OFFSET $2
      `;
      
      const result = await db.query(query, [limit, offset]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update verification status
   * @param {number} id - Verification ID
   * @param {string} status - New status
   * @param {number} reviewedBy - Reviewer user ID
   * @param {string} reviewerNotes - Review notes
   * @param {string} rejectionReason - Rejection reason (if rejected)
   * @returns {Promise<object|null>} - Updated verification record
   */
  static async updateStatus(id, status, reviewedBy, reviewerNotes = null, rejectionReason = null) {
    try {
      const query = `
        UPDATE business_verification 
        SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP,
            reviewer_notes = $3, rejection_reason = $4, updated_at = CURRENT_TIMESTAMP
        WHERE id = $5
        RETURNING *
      `;

      const result = await db.query(query, [status, reviewedBy, reviewerNotes, rejectionReason, id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update verification to under review
   * @param {number} id - Verification ID
   * @param {number} reviewedBy - Reviewer user ID
   * @returns {Promise<object|null>} - Updated verification record
   */
  static async markUnderReview(id, reviewedBy) {
    try {
      const query = `
        UPDATE business_verification 
        SET status = 'under_review', reviewed_by = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND status = 'pending'
        RETURNING *
      `;

      const result = await db.query(query, [reviewedBy, id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Approve verification
   * @param {number} id - Verification ID
   * @param {number} reviewedBy - Reviewer user ID
   * @param {string} reviewerNotes - Review notes
   * @returns {Promise<object|null>} - Updated verification record
   */
  static async approve(id, reviewedBy, reviewerNotes = null) {
    try {
      const result = await this.updateStatus(id, 'approved', reviewedBy, reviewerNotes);
      
      if (result) {
        // Update institute verification status if this is a primary verification
        if (result.verification_type === 'business_registration') {
          await db.query(`
            UPDATE institutes 
            SET verification_status = 'verified', status = 'verified'
            WHERE id = $1
          `, [result.institute_id]);
        }
      }
      
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reject verification
   * @param {number} id - Verification ID
   * @param {number} reviewedBy - Reviewer user ID
   * @param {string} rejectionReason - Rejection reason
   * @param {string} reviewerNotes - Review notes
   * @returns {Promise<object|null>} - Updated verification record
   */
  static async reject(id, reviewedBy, rejectionReason, reviewerNotes = null) {
    try {
      const result = await this.updateStatus(id, 'rejected', reviewedBy, reviewerNotes, rejectionReason);
      
      if (result) {
        // Update institute verification status
        await db.query(`
          UPDATE institutes 
          SET verification_status = 'rejected'
          WHERE id = $1
        `, [result.institute_id]);
      }
      
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if institute has required verifications
   * @param {number} instituteId - Institute ID
   * @returns {Promise<object>} - Verification status summary
   */
  static async getVerificationStatus(instituteId) {
    try {
      const query = `
        SELECT 
          verification_type,
          status,
          document_expiry_date,
          COUNT(*) as count
        FROM business_verification
        WHERE institute_id = $1
        GROUP BY verification_type, status, document_expiry_date
        ORDER BY verification_type, status
      `;
      
      const result = await db.query(query, [instituteId]);
      
      const verifications = result.rows;
      const requiredTypes = ['business_registration', 'tax_certificate', 'health_permit'];
      
      const status = {
        hasBusinessRegistration: false,
        hasTaxCertificate: false,
        hasHealthPermit: false,
        allRequired: false,
        expiringSoon: [],
        expired: []
      };

      const now = new Date();
      const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      verifications.forEach(verification => {
        if (verification.status === 'approved') {
          switch (verification.verification_type) {
            case 'business_registration':
              status.hasBusinessRegistration = true;
              break;
            case 'tax_certificate':
              status.hasTaxCertificate = true;
              break;
            case 'health_permit':
              status.hasHealthPermit = true;
              break;
          }

          // Check expiry dates
          if (verification.document_expiry_date) {
            const expiryDate = new Date(verification.document_expiry_date);
            if (expiryDate < now) {
              status.expired.push(verification.verification_type);
            } else if (expiryDate < thirtyDaysFromNow) {
              status.expiringSoon.push(verification.verification_type);
            }
          }
        }
      });

      status.allRequired = status.hasBusinessRegistration && status.hasTaxCertificate && status.hasHealthPermit;

      return status;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get verification statistics
   * @returns {Promise<object>} - Verification statistics
   */
  static async getStatistics() {
    try {
      const query = `
        SELECT 
          status,
          verification_type,
          COUNT(*) as count
        FROM business_verification
        GROUP BY status, verification_type
        ORDER BY status, verification_type
      `;
      
      const result = await db.query(query);
      
      const stats = {
        total: 0,
        pending: 0,
        underReview: 0,
        approved: 0,
        rejected: 0,
        byType: {}
      };

      result.rows.forEach(row => {
        stats.total += parseInt(row.count);
        
        switch (row.status) {
          case 'pending':
            stats.pending += parseInt(row.count);
            break;
          case 'under_review':
            stats.underReview += parseInt(row.count);
            break;
          case 'approved':
            stats.approved += parseInt(row.count);
            break;
          case 'rejected':
            stats.rejected += parseInt(row.count);
            break;
        }

        if (!stats.byType[row.verification_type]) {
          stats.byType[row.verification_type] = {};
        }
        stats.byType[row.verification_type][row.status] = parseInt(row.count);
      });

      return stats;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete verification record
   * @param {number} id - Verification ID
   * @returns {Promise<boolean>} - True if deleted
   */
  static async deleteById(id) {
    try {
      const query = `
        DELETE FROM business_verification
        WHERE id = $1
        RETURNING id
      `;
      
      const result = await db.query(query, [id]);
      return result.rows.length > 0;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = BusinessVerification;
