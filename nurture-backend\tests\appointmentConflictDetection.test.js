const assert = require('assert');

/**
 * Test Suite: Appointment Conflict Detection
 * Tests for strict date and time comparison
 */

// Helper function to normalize time to 24-hour format
function normalizeTo24Hr(timeStr) {
  if (!timeStr) return '';

  // If already in HH:MM or HH:MM:SS format, extract HH:MM
  if (timeStr.match(/^\d{1,2}:\d{2}(:\d{2})?$/)) {
    const parts = timeStr.split(':');
    return `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}`;
  }

  // Try to parse 12-hour format (e.g., "5:00 PM")
  if (timeStr.match(/\d{1,2}:\d{2}\s*(AM|PM|am|pm)/i)) {
    try {
      const parsed = new Date(`1970-01-01 ${timeStr}`);
      if (!isNaN(parsed.getTime())) {
        const hours = String(parsed.getHours()).padStart(2, '0');
        const minutes = String(parsed.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
      }
    } catch (e) {
      console.error(`⚠️ Error parsing time: ${timeStr}`, e);
    }
  }

  return timeStr;
}

// Helper function to convert time string to minutes
function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

// Run tests
if (require.main === module) {
  console.log('🧪 Running Appointment Conflict Detection Tests...\n');

  let passedTests = 0;
  let failedTests = 0;

  const runTest = (testName, testFn) => {
    try {
      console.log(`\n📋 ${testName}`);
      testFn();
      passedTests++;
      console.log(`✅ ${testName} PASSED`);
    } catch (error) {
      failedTests++;
      console.error(`❌ ${testName} FAILED: ${error.message}`);
    }
  };

  // Run all test suites
  runTest('Date Comparison', () => {
    const date1 = '2024-10-25';
    const date2 = '2024-10-25';
    assert.strictEqual(date1, date2);
  });

  runTest('Time Normalization', () => {
    const time1 = '17:00:00';
    const time2 = '5:00 PM';
    const normalized1 = normalizeTo24Hr(time1);
    const normalized2 = normalizeTo24Hr(time2);
    assert.strictEqual(normalized1, normalized2);
  });

  runTest('Time Slot Overlap Detection', () => {
    const existingStart = timeToMinutes('14:00');
    const existingEnd = existingStart + 60;
    const slotStart = timeToMinutes('14:30');
    const slotEnd = slotStart + 30;
    const hasOverlap = (slotStart < existingEnd) && (slotEnd > existingStart);
    assert.strictEqual(hasOverlap, true);
  });

  runTest('Operating Hours Validation', () => {
    const operatingStart = timeToMinutes('09:00');
    const operatingEnd = timeToMinutes('18:00');
    const appointmentTime = timeToMinutes('14:00');
    const isValid = appointmentTime >= operatingStart && appointmentTime < operatingEnd;
    assert.strictEqual(isValid, true);
  });

  runTest('Strict Comparison Logic', () => {
    const existingDate = '2024-10-25';
    const existingTime = '14:00:00';
    const requestedDate = '2024-10-25';
    const requestedTime = '14:00';

    const normalizedExisting = normalizeTo24Hr(existingTime);
    const normalizedRequested = normalizeTo24Hr(requestedTime);

    const hasConflict = existingDate === requestedDate && normalizedExisting === normalizedRequested;
    assert.strictEqual(hasConflict, true);
  });

  console.log(`\n\n📊 Test Results:`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Total: ${passedTests + failedTests}`);

  if (failedTests === 0) {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some tests failed!');
    process.exit(1);
  }
}

module.exports = {
  normalizeTo24Hr,
  timeToMinutes
};

