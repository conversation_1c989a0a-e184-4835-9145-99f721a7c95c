const express = require('express');
const router = express.Router();
const { logError } = require('../utils/errorLogger');

/**
 * Test endpoint to create an error that will be visible in admin portal
 * @route POST /api/test-error
 */
router.post('/', async (req, res, next) => {
  try {
    const { errorType = 'TestError', message = 'This is a test error from mobile app' } = req.body;

    // Log the error manually
    await logError({
      errorType: errorType,
      errorMessage: message,
      stackTrace: new Error(message).stack,
      endpoint: '/api/test-error',
      method: 'POST',
      userId: req.user?.id || null,
      userType: req.user?.userType || 'guest',
      requestBody: req.body,
      ipAddress: req.ip || req.connection?.remoteAddress,
      userAgent: req.get('user-agent'),
      statusCode: 500,
      severity: 'error'
    });

    res.status(200).json({
      success: true,
      message: 'Test error logged successfully! Check admin portal Error Logs page.',
      errorLogged: {
        type: errorType,
        message: message,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Test endpoint that throws an actual error (caught by error handler middleware)
 * @route POST /api/test-error/throw
 */
router.post('/throw', (req, res, next) => {
  const { message = 'This is a thrown test error' } = req.body;
  
  // Create and throw an error
  const error = new Error(message);
  error.statusCode = 500;
  error.name = 'TestError';
  
  // This will be caught by the error handler middleware
  next(error);
});

/**
 * Test endpoint to simulate a critical error
 * @route POST /api/test-error/critical
 */
router.post('/critical', async (req, res, next) => {
  try {
    const { message = 'Critical system error - Database connection failed' } = req.body;

    await logError({
      errorType: 'CriticalError',
      errorMessage: message,
      stackTrace: new Error(message).stack,
      endpoint: '/api/test-error/critical',
      method: 'POST',
      userId: req.user?.id || null,
      userType: req.user?.userType || 'guest',
      requestBody: req.body,
      ipAddress: req.ip || req.connection?.remoteAddress,
      userAgent: req.get('user-agent'),
      statusCode: 500,
      severity: 'critical'
    });

    res.status(200).json({
      success: true,
      message: 'Critical error logged! Check admin portal.',
      severity: 'critical'
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Test endpoint to simulate a validation error
 * @route POST /api/test-error/validation
 */
router.post('/validation', async (req, res, next) => {
  try {
    await logError({
      errorType: 'ValidationError',
      errorMessage: 'Validation failed: Email and password are required',
      endpoint: '/api/test-error/validation',
      method: 'POST',
      userId: req.user?.id || null,
      requestBody: req.body,
      ipAddress: req.ip || req.connection?.remoteAddress,
      userAgent: req.get('user-agent'),
      statusCode: 400,
      severity: 'warning'
    });

    res.status(200).json({
      success: true,
      message: 'Validation error logged! Check admin portal.',
      severity: 'warning'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

