const db = require('../config/database');

/**
 * PrivacyPolicy Model
 * Handles all database operations for privacy policy and terms & conditions
 */
class PrivacyPolicy {
  /**
   * Get latest privacy policy (by timestamp, NOT by is_active flag)
   * @param {string} language - Language code (en, fr, etc.)
   * @returns {Promise<object|null>} - Latest privacy policy or null
   */
  static async getActive(language = 'en') {
    try {
      // Get the most recent policy by timestamp and return content based on language
      const query = `
        SELECT
          id,
          CASE WHEN $1 = 'fr' THEN title_fr ELSE title_en END as title,
          CASE WHEN $1 = 'fr' THEN content_fr ELSE content_en END as content,
          title_en,
          title_fr,
          content_en,
          content_fr,
          url_slug,
          version,
          is_active,
          created_by,
          updated_by,
          created_at,
          updated_at
        FROM privacy_policy
        ORDER BY
          COALESCE(updated_at, created_at) DESC,
          created_at DESC
        LIMIT 1
      `;

      const result = await db.query(query, [language]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get privacy policy by URL slug (returns latest if multiple exist)
   * @param {string} urlSlug - URL slug
   * @param {string} language - Language code (en, fr)
   * @returns {Promise<object|null>} - Privacy policy or null
   */
  static async getBySlug(urlSlug, language = 'en') {
    try {
      const query = `
        SELECT
          id,
          CASE WHEN $2 = 'fr' THEN title_fr ELSE title_en END as title,
          CASE WHEN $2 = 'fr' THEN content_fr ELSE content_en END as content,
          title_en,
          title_fr,
          content_en,
          content_fr,
          url_slug,
          version,
          is_active,
          created_by,
          updated_by,
          created_at,
          updated_at
        FROM privacy_policy
        WHERE url_slug = $1
        ORDER BY
          COALESCE(updated_at, created_at) DESC,
          created_at DESC
        LIMIT 1
      `;

      const result = await db.query(query, [urlSlug, language]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get privacy policy by ID
   * @param {number} id - Privacy policy ID
   * @param {string} language - Language code (en, fr)
   * @returns {Promise<object|null>} - Privacy policy or null
   */
  static async getById(id, language = 'en') {
    try {
      const query = `
        SELECT
          id,
          CASE WHEN $2 = 'fr' THEN title_fr ELSE title_en END as title,
          CASE WHEN $2 = 'fr' THEN content_fr ELSE content_en END as content,
          title_en,
          title_fr,
          content_en,
          content_fr,
          url_slug,
          version,
          is_active,
          created_by,
          updated_by,
          created_at,
          updated_at
        FROM privacy_policy
        WHERE id = $1
      `;

      const result = await db.query(query, [id, language]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all privacy policies with pagination
   * @param {object} filters - Filter options
   * @returns {Promise<object>} - Privacy policies with pagination info
   */
  static async getAll(filters = {}) {
    try {
      const { page = 1, limit = 10, search = '' } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = [];
      let queryParams = [];
      let paramIndex = 1;

      if (search) {
        whereConditions.push(`(title_en ILIKE $${paramIndex} OR title_fr ILIKE $${paramIndex} OR content_en ILIKE $${paramIndex} OR content_fr ILIKE $${paramIndex})`);
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0
        ? `WHERE ${whereConditions.join(' AND ')}`
        : '';

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM privacy_policy
        ${whereClause}
      `;
      const countResult = await db.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const dataQuery = `
        SELECT
          id, title_en, title_fr, content_en, content_fr, url_slug, version, is_active,
          created_by, updated_by, created_at, updated_at
        FROM privacy_policy
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataResult = await db.query(dataQuery, [...queryParams, limit, offset]);

      return {
        policies: dataResult.rows,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new privacy policy
   * @param {object} policyData - Privacy policy data
   * @returns {Promise<object>} - Created privacy policy
   */
  static async create(policyData) {
    try {
      const {
        titleEn,
        titleFr,
        contentEn,
        contentFr,
        urlSlug,
        version = '1.0',
        isActive = true,
        createdBy
      } = policyData;

      // If this policy is being set as active, deactivate all others
      if (isActive) {
        await db.query('UPDATE privacy_policy SET is_active = false');
      }

      const query = `
        INSERT INTO privacy_policy (
          title_en, title_fr, content_en, content_fr, url_slug, version, is_active, created_by, updated_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
        RETURNING
          id, title_en, title_fr, content_en, content_fr, url_slug, version, is_active,
          created_by, updated_by, created_at, updated_at
      `;

      const values = [titleEn, titleFr, contentEn, contentFr, urlSlug, version, isActive, createdBy];
      const result = await db.query(query, values);

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update privacy policy
   * @param {number} id - Privacy policy ID
   * @param {object} updateData - Data to update
   * @param {number} updatedBy - Admin ID who is updating
   * @returns {Promise<object|null>} - Updated privacy policy or null
   */
  static async update(id, updateData, updatedBy) {
    try {
      const allowedFields = ['title_en', 'title_fr', 'content_en', 'content_fr', 'url_slug', 'version', 'is_active'];
      const updates = [];
      const values = [];
      let paramIndex = 1;

      // If setting this policy as active, deactivate all others first
      if (updateData.is_active === true || updateData.isActive === true) {
        await db.query('UPDATE privacy_policy SET is_active = false WHERE id != $1', [id]);
      }

      for (const [key, value] of Object.entries(updateData)) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        if (allowedFields.includes(snakeKey)) {
          updates.push(`${snakeKey} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      if (updates.length === 0) {
        return await this.getById(id);
      }

      // Add updated_by
      updates.push(`updated_by = $${paramIndex}`);
      values.push(updatedBy);
      paramIndex++;

      // Add id for WHERE clause
      values.push(id);

      const query = `
        UPDATE privacy_policy
        SET ${updates.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING
          id, title_en, title_fr, content_en, content_fr, url_slug, version, is_active,
          created_by, updated_by, created_at, updated_at
      `;

      const result = await db.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete privacy policy
   * @param {number} id - Privacy policy ID
   * @returns {Promise<boolean>} - True if deleted
   */
  static async delete(id) {
    try {
      const query = 'DELETE FROM privacy_policy WHERE id = $1';
      const result = await db.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Set policy as active (deactivates all others)
   * @param {number} id - Privacy policy ID
   * @returns {Promise<object|null>} - Updated privacy policy or null
   */
  static async setActive(id) {
    try {
      // Deactivate all policies
      await db.query('UPDATE privacy_policy SET is_active = false');

      // Activate the specified policy
      const query = `
        UPDATE privacy_policy
        SET is_active = true
        WHERE id = $1
        RETURNING
          id, title_en, title_fr, content_en, content_fr, url_slug, version, is_active,
          created_by, updated_by, created_at, updated_at
      `;

      const result = await db.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PrivacyPolicy;

