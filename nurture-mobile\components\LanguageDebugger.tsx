/**
 * Language Debugger Component
 * 
 * Add this component to any page to see the current language state
 * and help debug why the toggle button might not be showing.
 * 
 * Usage: <LanguageDebugger />
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAuthLanguage } from './AuthLanguageToggle';
import { useLanguageStore } from '../lib/stores/languageStore';

export const LanguageDebugger: React.FC = () => {
  const { language: authLanguage, isLoading: authLoading } = useAuthLanguage();
  const {
    detectedLanguage,
    locationBasedLanguage,
    isDetectingLocation,
    currentAccountType,
    userLanguage,
    instituteLanguage
  } = useLanguageStore();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔍 Language Debug Info</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Auth Language:</Text>
        <Text style={styles.value}>Current: {authLanguage || 'null'}</Text>
        <Text style={styles.value}>Loading: {authLoading ? 'Yes' : 'No'}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Language Store:</Text>
        <Text style={styles.value}>Detected: {detectedLanguage || 'null'}</Text>
        <Text style={styles.value}>Location-based: {locationBasedLanguage || 'null'}</Text>
        <Text style={styles.value}>User language: {userLanguage || 'null'}</Text>
        <Text style={styles.value}>Institute language: {instituteLanguage || 'null'}</Text>
        <Text style={styles.value}>Detecting location: {isDetectingLocation ? 'Yes' : 'No'}</Text>
        <Text style={styles.value}>Account type: {currentAccountType || 'null'}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Expected Behavior:</Text>
        <Text style={styles.value}>
          Button should show: Yes (All locations)
        </Text>
        <Text style={styles.value}>
          Button text: {authLanguage === 'en' ? 'French' : 'English'}
        </Text>
        <Text style={styles.value}>
          Button color: {authLanguage === 'en' ? 'Green' : 'Grey'}
        </Text>
        <Text style={styles.value}>
          Page should show: {authLanguage === 'fr' ? 'French text' : 'English text'}
        </Text>
        <Text style={styles.value}>
          Main app language: {currentAccountType === 'institute' ? instituteLanguage : userLanguage}
        </Text>
        <Text style={styles.value}>
          Languages synced: {authLanguage === (currentAccountType === 'institute' ? instituteLanguage : userLanguage) ? '✅ Yes' : '❌ No'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  section: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    color: '#333',
    marginLeft: 8,
    marginBottom: 2,
  },
});
