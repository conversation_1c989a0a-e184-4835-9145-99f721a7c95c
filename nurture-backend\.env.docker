# Docker Environment Configuration
# This file is used when running the application in Docker containers

# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Database Configuration (for containerized PostgreSQL)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=nurture_db
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Security
# Reduced from 12 to 10 for better performance while maintaining security
BCRYPT_ROUNDS=10

# Database Connection Pool Settings - optimized for performance
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000
