version: '3.8'

services:
  # Development override for backend service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    ports:
      - "${PORT:-3000}:3000"
    stdin_open: true
    tty: true

  # PostgreSQL with development settings
  postgres:
    environment:
      POSTGRES_DB: ${DB_NAME:-nurture_dev_db}
    ports:
      - "${DB_PORT:-5433}:5432"

  # Always include pgAdmin in development
  pgadmin:
    profiles: []
