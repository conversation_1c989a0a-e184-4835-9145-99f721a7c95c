server {
  listen 80;
  sendfile on;

  # Default MIME type for unspecified file types
  # It's optional to include, removing it may be better if issues persist
  # default_type application/octet-stream;

  gzip on;
  gzip_http_version 1.1;
  gzip_disable      "MSIE [1-6]\.";
  gzip_min_length   256;
  gzip_vary         on;
  gzip_proxied      expired no-cache no-store private auth;
  gzip_types        text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_comp_level   9;

  root /usr/share/nginx/html;

  location / {
    try_files $uri $uri/ /index.html =404;
  }

  # Serve static files with correct MIME types
  location ~* \.(css|js|jpg|jpeg|png|gif|ico)$ {
    expires 1w;
    access_log off;
    add_header Cache-Control "public";
  }
}
