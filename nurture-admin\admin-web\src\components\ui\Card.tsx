import React from 'react'
import { cn } from '../../lib/utils'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'elevated' | 'outlined'
}

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const cardVariants = {
  default: 'bg-white border border-gray-100',
  elevated: 'bg-white shadow-xl border border-gray-50 card-hover',
  outlined: 'bg-white border-2 border-gray-200 hover:border-gray-300 transition-colors duration-200',
}

export function Card({ children, className, variant = 'default', ...props }: CardProps) {
  return (
    <div
      className={cn(
        'rounded-2xl backdrop-blur-sm animate-fade-in',
        cardVariants[variant],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function CardHeader({ children, className, ...props }: CardHeaderProps) {
  return (
    <div
      className={cn('px-5 py-3 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white', className)}
      {...props}
    >
      {children}
    </div>
  )
}

export function CardContent({ children, className, ...props }: CardContentProps) {
  return (
    <div
      className={cn('px-5 py-3', className)}
      {...props}
    >
      {children}
    </div>
  )
}

export function CardFooter({ children, className, ...props }: CardFooterProps) {
  return (
    <div
      className={cn('px-8 py-6 border-t border-gray-100 bg-gradient-to-r from-white to-gray-50', className)}
      {...props}
    >
      {children}
    </div>
  )
}
