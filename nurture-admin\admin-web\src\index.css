@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure Tailwind base styles are applied */
@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }

  body {
    @apply antialiased;
  }

  /* Hide browser's built-in password reveal button */
  input[type="password"]::-ms-reveal,
  input[type="password"]::-ms-clear {
    display: none;
  }

  input[type="password"]::-webkit-credentials-auto-fill-button,
  input[type="password"]::-webkit-strong-password-auto-fill-button {
    display: none !important;
  }

  /* Firefox password reveal button */
  input[type="password"]::-moz-reveal {
    display: none;
  }

  /* Specific class to disable password reveal */
  .password-no-reveal::-ms-reveal,
  .password-no-reveal::-ms-clear {
    display: none;
  }

  .password-no-reveal::-webkit-credentials-auto-fill-button,
  .password-no-reveal::-webkit-strong-password-auto-fill-button {
    display: none !important;
  }

  .password-no-reveal::-moz-reveal {
    display: none;
  }

  /* Ensure our custom eye icon is always visible and clickable */
  .password-no-reveal + div button {
    position: relative;
    z-index: 10;
  }
}

:root {
  font-family: Inter, system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  color-scheme: light;
  color: #1F2937;
  background-color: #FFFFFF;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Professional shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Professional gradients */
  --gradient-primary: linear-gradient(135deg, #007A5E 0%, #005C46 100%);
  --gradient-secondary: linear-gradient(135deg, #FCD116 0%, #E6BC14 100%);
  --gradient-surface: linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

* {
  box-sizing: border-box;
}

/* Professional scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #D1D5DB, #9CA3AF);
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #9CA3AF, #6B7280);
}

::-webkit-scrollbar-corner {
  background: #F3F4F6;
}

/* Professional focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px #007A5E, 0 0 0 4px rgba(0, 122, 94, 0.2);
}

/* Professional animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

/* Professional glass effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Professional button effects */
.btn-primary {
  background: var(--gradient-primary);
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--gradient-secondary);
  transition: all 0.2s ease-in-out;
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
