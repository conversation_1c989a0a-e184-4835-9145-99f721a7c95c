import React from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { cn } from '@/lib/utils/cn';

interface LoadingProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export function Loading({ 
  size = 'large', 
  color = '#007A5E', 
  text, 
  className,
  fullScreen = false 
}: LoadingProps) {
  const content = (
    <View className={cn('items-center justify-center', className)}>
      <ActivityIndicator size={size} color={color} />
      {text && (
        <Text className="mt-2 text-gray-600 text-center">{text}</Text>
      )}
    </View>
  );

  if (fullScreen) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        {content}
      </View>
    );
  }

  return content;
}

export function LoadingSkeleton({ className }: { className?: string }) {
  return (
    <View className={cn('bg-gray-200 rounded animate-pulse', className)} />
  );
}

// Skeleton loader for appointment cards
export function AppointmentCardSkeleton() {
  return (
    <View className="bg-white rounded-lg p-4 mb-3 shadow-sm">
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1">
          <LoadingSkeleton className="h-5 w-3/4 mb-2" />
          <LoadingSkeleton className="h-4 w-1/2" />
        </View>
        <LoadingSkeleton className="h-6 w-16 rounded-full" />
      </View>
      <View className="flex-row items-center mb-2">
        <LoadingSkeleton className="h-4 w-4 rounded-full mr-2" />
        <LoadingSkeleton className="h-4 w-1/3" />
      </View>
      <View className="flex-row items-center">
        <LoadingSkeleton className="h-4 w-4 rounded-full mr-2" />
        <LoadingSkeleton className="h-4 w-1/4" />
      </View>
    </View>
  );
}

// Skeleton loader for institute cards
export function InstituteCardSkeleton() {
  return (
    <View className="bg-white rounded-lg p-4 mb-3 shadow-sm">
      <View className="flex-row mb-3">
        <LoadingSkeleton className="h-16 w-16 rounded-lg mr-3" />
        <View className="flex-1">
          <LoadingSkeleton className="h-5 w-3/4 mb-2" />
          <LoadingSkeleton className="h-4 w-1/2 mb-1" />
          <LoadingSkeleton className="h-4 w-2/3" />
        </View>
      </View>
      <View className="flex-row justify-between items-center">
        <LoadingSkeleton className="h-4 w-1/4" />
        <LoadingSkeleton className="h-8 w-20 rounded" />
      </View>
    </View>
  );
}

// Skeleton loader for service cards
export function ServiceCardSkeleton() {
  return (
    <View className="bg-white rounded-lg p-4 mb-3 shadow-sm">
      <LoadingSkeleton className="h-5 w-2/3 mb-2" />
      <LoadingSkeleton className="h-4 w-full mb-2" />
      <LoadingSkeleton className="h-4 w-3/4 mb-3" />
      <View className="flex-row justify-between items-center">
        <LoadingSkeleton className="h-4 w-1/3" />
        <LoadingSkeleton className="h-6 w-16 rounded-full" />
      </View>
    </View>
  );
}

// Enhanced loading component for authentication flows
export function AuthLoading({
  text = 'Authenticating...',
  subText,
  showProgress = false,
  progress = 0
}: {
  text?: string;
  subText?: string;
  showProgress?: boolean;
  progress?: number;
}) {
  return (
    <View className="flex-1 items-center justify-center bg-white px-6">
      <View className="w-20 h-20 bg-primary rounded-full items-center justify-center mb-6">
        <ActivityIndicator size="large" color="white" />
      </View>
      <Text className="text-xl font-semibold text-gray-900 mb-2 text-center">
        {text}
      </Text>
      {subText && (
        <Text className="text-gray-600 text-center mb-4">
          {subText}
        </Text>
      )}
      {showProgress && (
        <View className="w-full max-w-xs">
          <View className="w-full bg-gray-200 rounded-full h-2">
            <View
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </View>
          <Text className="text-xs text-gray-500 text-center mt-2">
            {Math.round(progress)}%
          </Text>
        </View>
      )}
    </View>
  );
}
