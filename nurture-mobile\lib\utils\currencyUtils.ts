// Currency utilities for handling multiple currencies and country-to-currency mapping

export interface CurrencyInfo {
  code: string;
  name: string;
  symbol: string;
  flag: string;
  country: string;
}

// Comprehensive list of major world currencies
export const CURRENCIES: CurrencyInfo[] = [
  // Africa
  { code: 'XAF', name: 'Central African CFA Franc', symbol: 'FCFA', flag: '🇨🇲', country: 'Cameroon' },
  { code: 'XOF', name: 'West African CFA Franc', symbol: 'FCFA', flag: '🇸🇳', country: 'Senegal' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦', flag: '🇳🇬', country: 'Nigeria' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', flag: '🇿🇦', country: 'South Africa' },
  { code: 'EGP', name: 'Egyptian Pound', symbol: '£', flag: '🇪🇬', country: 'Egypt' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪', country: 'Kenya' },
  { code: 'GHS', name: 'Ghanaian <PERSON>di', symbol: '₵', flag: '🇬🇭', country: 'Ghana' },
  { code: 'ETB', name: 'Ethiopian Birr', symbol: 'Br', flag: '🇪🇹', country: 'Ethiopia' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh', flag: '🇺🇬', country: 'Uganda' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh', flag: '🇹🇿', country: 'Tanzania' },
  
  // Europe
  { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺', country: 'Germany' },
  { code: 'GBP', name: 'British Pound Sterling', symbol: '£', flag: '🇬🇧', country: 'United Kingdom' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', flag: '🇨🇭', country: 'Switzerland' },
  { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', flag: '🇳🇴', country: 'Norway' },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', flag: '🇸🇪', country: 'Sweden' },
  { code: 'DKK', name: 'Danish Krone', symbol: 'kr', flag: '🇩🇰', country: 'Denmark' },
  { code: 'PLN', name: 'Polish Złoty', symbol: 'zł', flag: '🇵🇱', country: 'Poland' },
  { code: 'CZK', name: 'Czech Koruna', symbol: 'Kč', flag: '🇨🇿', country: 'Czech Republic' },
  { code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft', flag: '🇭🇺', country: 'Hungary' },
  { code: 'RON', name: 'Romanian Leu', symbol: 'lei', flag: '🇷🇴', country: 'Romania' },
  
  // Americas
  { code: 'USD', name: 'US Dollar', symbol: '$', flag: '🇺🇸', country: 'United States' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', flag: '🇨🇦', country: 'Canada' },
  { code: 'BRL', name: 'Brazilian Real', symbol: 'R$', flag: '🇧🇷', country: 'Brazil' },
  { code: 'MXN', name: 'Mexican Peso', symbol: '$', flag: '🇲🇽', country: 'Mexico' },
  { code: 'ARS', name: 'Argentine Peso', symbol: '$', flag: '🇦🇷', country: 'Argentina' },
  { code: 'CLP', name: 'Chilean Peso', symbol: '$', flag: '🇨🇱', country: 'Chile' },
  { code: 'COP', name: 'Colombian Peso', symbol: '$', flag: '🇨🇴', country: 'Colombia' },
  { code: 'PEN', name: 'Peruvian Sol', symbol: 'S/', flag: '🇵🇪', country: 'Peru' },
  
  // Asia
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', flag: '🇨🇳', country: 'China' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵', country: 'Japan' },
  { code: 'KRW', name: 'South Korean Won', symbol: '₩', flag: '🇰🇷', country: 'South Korea' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', flag: '🇮🇳', country: 'India' },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$', flag: '🇸🇬', country: 'Singapore' },
  { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$', flag: '🇭🇰', country: 'Hong Kong' },
  { code: 'THB', name: 'Thai Baht', symbol: '฿', flag: '🇹🇭', country: 'Thailand' },
  { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM', flag: '🇲🇾', country: 'Malaysia' },
  { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', flag: '🇮🇩', country: 'Indonesia' },
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱', flag: '🇵🇭', country: 'Philippines' },
  { code: 'VND', name: 'Vietnamese Dong', symbol: '₫', flag: '🇻🇳', country: 'Vietnam' },
  { code: 'PKR', name: 'Pakistani Rupee', symbol: '₨', flag: '🇵🇰', country: 'Pakistan' },
  { code: 'BDT', name: 'Bangladeshi Taka', symbol: '৳', flag: '🇧🇩', country: 'Bangladesh' },
  { code: 'LKR', name: 'Sri Lankan Rupee', symbol: '₨', flag: '🇱🇰', country: 'Sri Lanka' },
  
  // Middle East
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ', flag: '🇦🇪', country: 'United Arab Emirates' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: '﷼', flag: '🇸🇦', country: 'Saudi Arabia' },
  { code: 'QAR', name: 'Qatari Riyal', symbol: '﷼', flag: '🇶🇦', country: 'Qatar' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك', flag: '🇰🇼', country: 'Kuwait' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب', flag: '🇧🇭', country: 'Bahrain' },
  { code: 'OMR', name: 'Omani Rial', symbol: '﷼', flag: '🇴🇲', country: 'Oman' },
  { code: 'JOD', name: 'Jordanian Dinar', symbol: 'د.ا', flag: '🇯🇴', country: 'Jordan' },
  { code: 'LBP', name: 'Lebanese Pound', symbol: '£', flag: '🇱🇧', country: 'Lebanon' },
  { code: 'ILS', name: 'Israeli Shekel', symbol: '₪', flag: '🇮🇱', country: 'Israel' },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺', flag: '🇹🇷', country: 'Turkey' },
  
  // Oceania
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', flag: '🇦🇺', country: 'Australia' },
  { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$', flag: '🇳🇿', country: 'New Zealand' },
  
  // Russia & Eastern Europe
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽', flag: '🇷🇺', country: 'Russia' },
  { code: 'UAH', name: 'Ukrainian Hryvnia', symbol: '₴', flag: '🇺🇦', country: 'Ukraine' },
];

// Country to currency mapping
export const COUNTRY_CURRENCY_MAP: Record<string, string> = {
  // Africa - CFA Franc Zone (Central)
  'Cameroon': 'XAF',
  'Central African Republic': 'XAF',
  'Chad': 'XAF',
  'Republic of the Congo': 'XAF',
  'Equatorial Guinea': 'XAF',
  'Gabon': 'XAF',
  
  // Africa - CFA Franc Zone (West)
  'Benin': 'XOF',
  'Burkina Faso': 'XOF',
  'Côte d\'Ivoire': 'XOF',
  'Guinea-Bissau': 'XOF',
  'Mali': 'XOF',
  'Niger': 'XOF',
  'Senegal': 'XOF',
  'Togo': 'XOF',
  
  // Africa - Other
  'Nigeria': 'NGN',
  'South Africa': 'ZAR',
  'Egypt': 'EGP',
  'Kenya': 'KES',
  'Ghana': 'GHS',
  'Ethiopia': 'ETB',
  'Uganda': 'UGX',
  'Tanzania': 'TZS',
  'Morocco': 'MAD',
  'Algeria': 'DZD',
  'Tunisia': 'TND',
  'Libya': 'LYD',
  'Sudan': 'SDG',
  'Angola': 'AOA',
  'Mozambique': 'MZN',
  'Zimbabwe': 'ZWL',
  'Botswana': 'BWP',
  'Namibia': 'NAD',
  'Zambia': 'ZMW',
  'Malawi': 'MWK',
  'Rwanda': 'RWF',
  'Burundi': 'BIF',
  
  // Europe - Eurozone
  'Germany': 'EUR',
  'France': 'EUR',
  'Italy': 'EUR',
  'Spain': 'EUR',
  'Netherlands': 'EUR',
  'Belgium': 'EUR',
  'Austria': 'EUR',
  'Portugal': 'EUR',
  'Finland': 'EUR',
  'Ireland': 'EUR',
  'Greece': 'EUR',
  'Luxembourg': 'EUR',
  'Slovenia': 'EUR',
  'Slovakia': 'EUR',
  'Estonia': 'EUR',
  'Latvia': 'EUR',
  'Lithuania': 'EUR',
  'Malta': 'EUR',
  'Cyprus': 'EUR',
  
  // Europe - Non-Eurozone
  'United Kingdom': 'GBP',
  'Switzerland': 'CHF',
  'Norway': 'NOK',
  'Sweden': 'SEK',
  'Denmark': 'DKK',
  'Poland': 'PLN',
  'Czech Republic': 'CZK',
  'Hungary': 'HUF',
  'Romania': 'RON',
  'Bulgaria': 'BGN',
  'Croatia': 'HRK',
  'Serbia': 'RSD',
  'Bosnia and Herzegovina': 'BAM',
  'North Macedonia': 'MKD',
  'Albania': 'ALL',
  'Montenegro': 'EUR',
  'Moldova': 'MDL',
  'Ukraine': 'UAH',
  'Belarus': 'BYN',
  'Russia': 'RUB',
  'Turkey': 'TRY',
  
  // Americas
  'United States': 'USD',
  'Canada': 'CAD',
  'Mexico': 'MXN',
  'Brazil': 'BRL',
  'Argentina': 'ARS',
  'Chile': 'CLP',
  'Colombia': 'COP',
  'Peru': 'PEN',
  'Venezuela': 'VES',
  'Ecuador': 'USD',
  'Uruguay': 'UYU',
  'Paraguay': 'PYG',
  'Bolivia': 'BOB',
  'Guyana': 'GYD',
  'Suriname': 'SRD',
  'French Guiana': 'EUR',
  
  // Asia
  'China': 'CNY',
  'Japan': 'JPY',
  'South Korea': 'KRW',
  'India': 'INR',
  'Indonesia': 'IDR',
  'Thailand': 'THB',
  'Malaysia': 'MYR',
  'Singapore': 'SGD',
  'Philippines': 'PHP',
  'Vietnam': 'VND',
  'Bangladesh': 'BDT',
  'Pakistan': 'PKR',
  'Sri Lanka': 'LKR',
  'Myanmar': 'MMK',
  'Cambodia': 'KHR',
  'Laos': 'LAK',
  'Nepal': 'NPR',
  'Bhutan': 'BTN',
  'Maldives': 'MVR',
  'Afghanistan': 'AFN',
  'Mongolia': 'MNT',
  'North Korea': 'KPW',
  'Taiwan': 'TWD',
  'Hong Kong': 'HKD',
  'Macau': 'MOP',
  
  // Middle East
  'Saudi Arabia': 'SAR',
  'United Arab Emirates': 'AED',
  'Qatar': 'QAR',
  'Kuwait': 'KWD',
  'Bahrain': 'BHD',
  'Oman': 'OMR',
  'Jordan': 'JOD',
  'Lebanon': 'LBP',
  'Syria': 'SYP',
  'Iraq': 'IQD',
  'Iran': 'IRR',
  'Israel': 'ILS',
  'Palestine': 'ILS',
  'Yemen': 'YER',
  
  // Oceania
  'Australia': 'AUD',
  'New Zealand': 'NZD',
  'Fiji': 'FJD',
  'Papua New Guinea': 'PGK',
  'Solomon Islands': 'SBD',
  'Vanuatu': 'VUV',
  'Samoa': 'WST',
  'Tonga': 'TOP',
  'Palau': 'USD',
  'Marshall Islands': 'USD',
  'Micronesia': 'USD',
  'Kiribati': 'AUD',
  'Tuvalu': 'AUD',
  'Nauru': 'AUD',
};

/**
 * Get currency information by currency code
 */
export const getCurrencyInfo = (currencyCode: string): CurrencyInfo | undefined => {
  return CURRENCIES.find(currency => currency.code === currencyCode);
};

/**
 * Get default currency for a country
 */
export const getCurrencyForCountry = (country: string): string => {
  return COUNTRY_CURRENCY_MAP[country] || 'XAF'; // Default to XAF (Cameroon)
};

/**
 * Format currency amount with proper symbol and formatting
 */
export const formatCurrency = (amount: number, currencyCode: string = 'XAF'): string => {
  const currencyInfo = getCurrencyInfo(currencyCode);
  const symbol = currencyInfo?.symbol || currencyCode;
  
  // Format number with proper locale
  const formattedAmount = amount.toLocaleString();
  
  // Handle different currency symbol positions
  switch (currencyCode) {
    case 'USD':
    case 'CAD':
    case 'AUD':
    case 'NZD':
    case 'HKD':
    case 'SGD':
      return `${symbol}${formattedAmount}`;
    case 'EUR':
    case 'GBP':
      return `${symbol}${formattedAmount}`;
    case 'JPY':
    case 'CNY':
    case 'KRW':
      return `${symbol}${formattedAmount}`;
    case 'INR':
      return `${symbol}${formattedAmount}`;
    case 'XAF':
    case 'XOF':
    default:
      return `${formattedAmount} ${symbol}`;
  }
};

/**
 * Get popular currencies (most commonly used)
 */
export const getPopularCurrencies = (): CurrencyInfo[] => {
  const popularCodes = ['XAF', 'USD', 'EUR', 'GBP', 'NGN', 'ZAR', 'CAD', 'AUD', 'JPY', 'CNY'];
  return popularCodes.map(code => getCurrencyInfo(code)).filter(Boolean) as CurrencyInfo[];
};

/**
 * Search currencies by name or code
 */
export const searchCurrencies = (query: string): CurrencyInfo[] => {
  const searchTerm = query.toLowerCase();
  return CURRENCIES.filter(currency =>
    currency.name.toLowerCase().includes(searchTerm) ||
    currency.code.toLowerCase().includes(searchTerm) ||
    currency.country.toLowerCase().includes(searchTerm)
  );
};
