import { User, Institute, Service, Staff, Appointment } from '../types';

export const mockUser: User = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+237670123456',
  city: 'Douala',
  country: 'Cameroon',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  joinDate: '2024-01-15'
};

export const mockStaff: Staff[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    specialization: 'Massage Therapy',
    rating: 4.9,
    experience: '8 years',
    image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
    bio: 'Specialized in therapeutic and relaxation massage with extensive training in traditional techniques.'
  },
  {
    id: '2',
    name: 'Dr. <PERSON>',
    specialization: 'Physical Therapy',
    rating: 4.8,
    experience: '12 years',
    image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',
    bio: 'Expert in rehabilitation and sports injury recovery with modern treatment methods.'
  },
  {
    id: '3',
    name: 'Dr. Fatima Bello',
    specialization: 'Wellness Consultation',
    rating: 4.7,
    experience: '6 years',
    image: 'https://images.unsplash.com/photo-1594824388853-d0c2b8e3b5e8?w=150&h=150&fit=crop&crop=face',
    bio: 'Holistic wellness consultant focusing on mental health and lifestyle improvement.'
  },
  {
    id: '4',
    name: 'Dr. Emmanuel Tabi',
    specialization: 'Acupuncture',
    rating: 4.6,
    experience: '10 years',
    image: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face',
    bio: 'Traditional acupuncture specialist with certification in Chinese medicine practices.'
  },
  {
    id: '5',
    name: 'Dr. Grace Ndongo',
    specialization: 'Aromatherapy',
    rating: 4.8,
    experience: '5 years',
    image: 'https://images.unsplash.com/photo-**********-deb4988cc6c0?w=150&h=150&fit=crop&crop=face',
    bio: 'Certified aromatherapist specializing in essential oil treatments and relaxation therapy.'
  }
];

export const mockServices: Service[] = [
  {
    id: '1',
    name: 'Deep Tissue Massage',
    duration: '60 minutes',
    price: 25000,
    description: 'Therapeutic massage for muscle tension relief and improved circulation',
    category: 'Massage',
    staff: [mockStaff[0]],
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=300&h=200&fit=crop'
  },
  {
    id: '2',
    name: 'Swedish Massage',
    duration: '45 minutes',
    price: 20000,
    description: 'Relaxing full-body massage using gentle, flowing strokes',
    category: 'Massage',
    staff: [mockStaff[0]],
    image: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=300&h=200&fit=crop'
  },
  {
    id: '3',
    name: 'Physical Therapy Session',
    duration: '90 minutes',
    price: 35000,
    description: 'Comprehensive rehabilitation session for injury recovery',
    category: 'Therapy',
    staff: [mockStaff[1]],
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop'
  },
  {
    id: '4',
    name: 'Wellness Consultation',
    duration: '30 minutes',
    price: 15000,
    description: 'Personalized wellness assessment and lifestyle recommendations',
    category: 'Consultation',
    staff: [mockStaff[2]],
    image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop'
  },
  {
    id: '5',
    name: 'Acupuncture Treatment',
    duration: '75 minutes',
    price: 30000,
    description: 'Traditional acupuncture for pain relief and energy balance',
    category: 'Alternative Medicine',
    staff: [mockStaff[3]],
    image: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=300&h=200&fit=crop'
  },
  {
    id: '6',
    name: 'Aromatherapy Session',
    duration: '60 minutes',
    price: 22000,
    description: 'Relaxation therapy using essential oils and gentle massage',
    category: 'Aromatherapy',
    staff: [mockStaff[4]],
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop'
  },
  {
    id: '7',
    name: 'Deep Tissue Cleaning Service',
    duration: '90 minutes',
    price: 35000,
    description: 'Comprehensive deep tissue cleaning and therapeutic treatment',
    category: 'Therapy',
    staff: [mockStaff[0], mockStaff[1]],
    image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop'
  }
];

export const mockInstitutes: Institute[] = [
  // All mock institutes removed - using only real data from database
];

export const mockAppointments: Appointment[] = [
  {
    id: '1',
    instituteId: '1',
    instituteName: 'Zen Wellness Center',
    serviceId: '1',
    serviceName: 'Deep Tissue Massage',
    staffId: '1',
    staffName: 'Dr. Marie Nguemou',
    date: '2024-07-05',
    time: '2:00 PM',
    status: 'confirmed',
    price: 25000,
    notes: 'Focus on lower back tension'
  },
  {
    id: '2',
    instituteId: '3',
    instituteName: 'Vitality Physiotherapy Clinic',
    serviceId: '3',
    serviceName: 'Physical Therapy Session',
    staffId: '2',
    staffName: 'Dr. Paul Kamga',
    date: '2024-07-08',
    time: '10:00 AM',
    status: 'pending',
    price: 35000,
    notes: 'Follow-up session for knee injury'
  },
  {
    id: '3',
    instituteId: '2',
    instituteName: 'Harmony Health Spa',
    serviceId: '6',
    serviceName: 'Aromatherapy Session',
    staffId: '5',
    staffName: 'Dr. Grace Ndongo',
    date: '2024-06-28',
    time: '4:00 PM',
    status: 'completed',
    price: 22000,
    notes: 'Lavender essential oil treatment'
  }
];
