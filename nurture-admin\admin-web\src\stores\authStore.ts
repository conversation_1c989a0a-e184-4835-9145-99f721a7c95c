import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authApi } from '../lib/api'
import type { Admin } from '../types'

interface AuthState {
  user: Admin | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  clearError: () => void
  checkAuth: () => Promise<void>
  setLoading: (loading: boolean) => void
  updateUser: (user: Admin) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authApi.login(email, password)

          if (response.success && response.data) {
            const { admin, token } = response.data
            
            // Store token in localStorage for API interceptor
            localStorage.setItem('admin_token', token)
            localStorage.setItem('admin_user', JSON.stringify(admin))

            set({
              user: admin,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error(response.message || 'Login failed')
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || error.message || 'Login failed'
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null,
          })
          throw error
        }
      },

      logout: () => {
        // Clear localStorage
        localStorage.removeItem('admin_token')
        localStorage.removeItem('admin_user')
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        })
      },

      clearError: () => {
        set({ error: null })
      },

      checkAuth: async () => {
        const token = localStorage.getItem('admin_token')
        const userStr = localStorage.getItem('admin_user')

        if (!token || !userStr) {
          set({ isAuthenticated: false, user: null, token: null })
          return
        }

        try {
          const user = JSON.parse(userStr)

          // First set the auth state optimistically
          set({
            user,
            token,
            isAuthenticated: true,
            error: null,
          })

          // Then verify token is still valid in the background
          // If this fails, the API interceptor will handle the cleanup
          await authApi.getProfile()
        } catch (error) {
          console.log('Token verification failed:', error)
          // Only clear auth state if we're sure the token is invalid
          // The API interceptor will handle localStorage cleanup
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null,
          })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      updateUser: (user: Admin) => {
        // Update localStorage with the new user data
        localStorage.setItem('admin_user', JSON.stringify(user))
        set({ user })
      },
    }),
    {
      name: 'admin-auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
