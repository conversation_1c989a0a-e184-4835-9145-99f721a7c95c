/**
 * Centralized Currency Update Service
 * 
 * This service handles automatic currency updates across all connected systems
 * when an institute's country changes. It ensures consistency across:
 * - Institute services (custom and common)
 * - User booking interface
 * - Institute interface
 * - All cached data
 */

import { useInstituteStore } from '@/lib/stores/instituteStore';
import { useInstitutesStore } from '@/lib/stores/institutesStore';

export interface CurrencyUpdateResult {
  success: boolean;
  currency?: string;
  customServicesUpdated?: number;
  commonServicesUpdated?: number;
  generalServicesUpdated?: number;
  error?: string;
}

export class CurrencyUpdateService {
  private static instance: CurrencyUpdateService;
  private updateQueue: Map<string, Promise<CurrencyUpdateResult>> = new Map();

  private constructor() {}

  public static getInstance(): CurrencyUpdateService {
    if (!CurrencyUpdateService.instance) {
      CurrencyUpdateService.instance = new CurrencyUpdateService();
    }
    return CurrencyUpdateService.instance;
  }

  /**
   * Comprehensive currency update that handles all connected systems
   */
  public async updateAllConnectedSystems(
    instituteId: string,
    newCountry: string,
    context: 'auto-save' | 'manual-save' | 'page-leave' | 'immediate'
  ): Promise<CurrencyUpdateResult> {
    const updateKey = `${instituteId}-${newCountry}`;
    
    // Prevent duplicate updates for the same institute and country
    if (this.updateQueue.has(updateKey)) {
      console.log(`🔄 Currency update already in progress for institute ${instituteId} to ${newCountry}`);
      return this.updateQueue.get(updateKey)!;
    }

    const updatePromise = this.performComprehensiveUpdate(instituteId, newCountry, context);
    this.updateQueue.set(updateKey, updatePromise);

    try {
      const result = await updatePromise;
      return result;
    } finally {
      // Clean up the queue after completion
      setTimeout(() => {
        this.updateQueue.delete(updateKey);
      }, 5000); // Keep in queue for 5 seconds to prevent rapid duplicate calls
    }
  }

  private async performComprehensiveUpdate(
    instituteId: string,
    newCountry: string,
    context: string
  ): Promise<CurrencyUpdateResult> {
    console.log(`🌍 COMPREHENSIVE CURRENCY UPDATE [${context.toUpperCase()}]: Starting update for institute ${instituteId} to country: ${newCountry}`);

    try {
      // Step 1: Update service currencies in database
      const instituteStore = useInstituteStore.getState();
      const currencyResult = await instituteStore.updateServiceCurrenciesForCountry(
        parseInt(instituteId),
        newCountry
      );

      if (!currencyResult.success) {
        console.error('❌ Failed to update service currencies:', currencyResult.error);
        return {
          success: false,
          error: currencyResult.error || 'Failed to update service currencies'
        };
      }

      console.log(`✅ CURRENCY UPDATE SUCCESS [${context.toUpperCase()}]: Updated to ${currencyResult.currency}`);
      console.log(`   - Custom services updated: ${currencyResult.customServicesUpdated}`);
      console.log(`   - Common services updated: ${currencyResult.commonServicesUpdated}`);
      console.log(`   - General services updated: ${currencyResult.generalServicesUpdated}`);

      // Step 2: Refresh all connected data stores
      await this.refreshAllDataStores(instituteId);

      // Step 3: Clear any cached pricing data
      await this.clearCachedPricingData(instituteId);

      return {
        success: true,
        currency: currencyResult.currency,
        customServicesUpdated: currencyResult.customServicesUpdated,
        commonServicesUpdated: currencyResult.commonServicesUpdated,
        generalServicesUpdated: currencyResult.generalServicesUpdated
      };

    } catch (error) {
      console.error(`❌ COMPREHENSIVE CURRENCY UPDATE ERROR [${context.toUpperCase()}]:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Refresh all data stores to show updated currencies
   */
  private async refreshAllDataStores(instituteId: string): Promise<void> {
    console.log('🔄 Refreshing all data stores...');

    try {
      // Refresh institute store services (for institute interface)
      const instituteStore = useInstituteStore.getState();
      if (instituteStore.getInstituteServices) {
        await instituteStore.getInstituteServices(parseInt(instituteId));
        console.log('✅ Refreshed institute store services');
      }

      // Refresh institutes store services (for user booking interface)
      const institutesStore = useInstitutesStore.getState();
      if (institutesStore.fetchInstituteServices) {
        await institutesStore.fetchInstituteServices(instituteId);
        console.log('✅ Refreshed user booking service data');
      }

      // Refresh institute data to ensure country is updated
      if (instituteStore.getInstituteById) {
        await instituteStore.getInstituteById(parseInt(instituteId));
        console.log('✅ Refreshed institute data');
      }

    } catch (error) {
      console.warn('⚠️ Some data stores could not be refreshed:', error);
    }
  }

  /**
   * Clear cached pricing data that might contain old currencies
   */
  private async clearCachedPricingData(instituteId: string): Promise<void> {
    try {
      // Clear any cached pricing state for this institute
      const cacheKey = `institute_${instituteId}`;
      
      // Clear from global pricing cache if it exists
      if (typeof global !== 'undefined' && (global as any).pricingStateCache) {
        delete (global as any).pricingStateCache[cacheKey];
        console.log('✅ Cleared cached pricing data');
      }

    } catch (error) {
      console.warn('⚠️ Could not clear cached pricing data:', error);
    }
  }

  /**
   * Quick currency update for immediate UI feedback
   */
  public async quickUpdate(instituteId: string, newCountry: string): Promise<CurrencyUpdateResult> {
    return this.updateAllConnectedSystems(instituteId, newCountry, 'immediate');
  }

  /**
   * Auto-save currency update (triggered during typing)
   */
  public async autoSaveUpdate(instituteId: string, newCountry: string): Promise<CurrencyUpdateResult> {
    return this.updateAllConnectedSystems(instituteId, newCountry, 'auto-save');
  }

  /**
   * Manual save currency update (triggered by save button)
   */
  public async manualSaveUpdate(instituteId: string, newCountry: string): Promise<CurrencyUpdateResult> {
    return this.updateAllConnectedSystems(instituteId, newCountry, 'manual-save');
  }

  /**
   * Page leave currency update (triggered when navigating away)
   */
  public async pageLeaveUpdate(instituteId: string, newCountry: string): Promise<CurrencyUpdateResult> {
    return this.updateAllConnectedSystems(instituteId, newCountry, 'page-leave');
  }
}

// Export singleton instance
export const currencyUpdateService = CurrencyUpdateService.getInstance();

// Export convenience functions
export const updateCurrencyForAllSystems = (
  instituteId: string,
  newCountry: string,
  context: 'auto-save' | 'manual-save' | 'page-leave' | 'immediate' = 'immediate'
) => {
  return currencyUpdateService.updateAllConnectedSystems(instituteId, newCountry, context);
};
