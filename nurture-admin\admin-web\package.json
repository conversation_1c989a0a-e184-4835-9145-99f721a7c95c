{"name": "admin-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@types/node": "^24.2.1", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}