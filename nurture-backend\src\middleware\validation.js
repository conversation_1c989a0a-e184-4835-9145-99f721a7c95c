const { body, query } = require('express-validator');

/**
 * Validation rules for user registration
 */
const validateRegistration = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('Email must be less than 255 characters'),

  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 0, max: 100 })
    .withMessage('Last name must be less than 100 characters')
    .custom((value) => {
      if (value && !/^[a-zA-Z\s'-]+$/.test(value)) {
        throw new Error('Last name can only contain letters, spaces, hyphens, and apostrophes');
      }
      return true;
    }),

  body('phone')
    .optional()
    .trim(),

  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth in YYYY-MM-DD format')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120 years');
      }
      
      return true;
    }),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be one of: male, female, other')
];

/**
 * Validation rules for user login
 */
const validateLogin = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('password')
    .notEmpty()
    .withMessage('Password is required'),

  body('userType')
    .notEmpty()
    .withMessage('User type is required')
    .isIn(['user', 'institute'])
    .withMessage('User type must be either "user" or "institute"'),

  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('Remember me must be a boolean value')
];

/**
 * Validation rules for profile update
 */
const validateProfileUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 0, max: 100 })
    .withMessage('Last name must be less than 100 characters')
    .custom((value) => {
      if (value && !/^[a-zA-Z\s'-]+$/.test(value)) {
        throw new Error('Last name can only contain letters, spaces, hyphens, and apostrophes');
      }
      return true;
    }),

  body('phone')
    .optional()
    .trim()
    .matches(/^[\+]?[0-9\s\-\(\)]{7,20}$/)
    .withMessage('Please provide a valid phone number'),

  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date of birth in YYYY-MM-DD format')
    .custom((value) => {
      const birthDate = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120 years');
      }
      
      return true;
    }),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be one of: male, female, other'),

  body('profilePictureUrl')
    .optional()
    .isURL()
    .withMessage('Profile picture must be a valid URL')
];

/**
 * Validation rules for institute registration (institute data, not user account)
 */
const validateInstituteRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Institute name must be between 2 and 255 characters')
    .custom((value) => {
      // Block accented/diacritical characters (non-ASCII characters)
      if (/[^\x00-\x7F]/.test(value)) {
        throw new Error('Institute name cannot contain accented or special unicode characters (e.g., é, è, ê, ñ)');
      }
      // Allow alphanumeric, spaces, and common special characters
      if (!/^[a-zA-Z0-9\s!@#$%^&*()\-_+=\[\]{}|;:'",.<>?/~`\\]+$/.test(value)) {
        throw new Error('Institute name contains invalid characters');
      }
      return true;
    }),

  body('businessName')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Business name must not exceed 255 characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),

  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('phone')
    .trim()
    .isLength({ min: 8, max: 20 })
    .withMessage('Phone number must be between 8 and 20 characters'),

  body('website')
    .optional({ checkFalsy: true })
    .isURL()
    .withMessage('Please provide a valid website URL'),

  body('businessType')
    .optional()
    .isIn(['sole_proprietorship', 'partnership', 'corporation', 'llc', 'ngo', 'wellness_center', 'other'])
    .withMessage('Invalid business type'),

  body('servicesOffered')
    .optional()
    .isArray()
    .withMessage('Services offered must be an array'),

  body('priceRange')
    .optional()
    .isIn(['$', '$$', '$$$', '$$$$'])
    .withMessage('Price range must be $, $$, $$$, or $$$$'),

  // Location validation
  body('streetAddress')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Street address must be between 5 and 255 characters'),

  body('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City must be between 2 and 100 characters'),

  body('postalCode')
    .optional({ checkFalsy: true })
    .matches(/^\d{4,10}$/)
    .withMessage('Postal code must be 4-10 digits')
];

/**
 * Validation rules for institute update
 */
const validateInstituteUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Institute name must be between 2 and 255 characters'),

  body('businessName')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Business name must not exceed 255 characters'),

  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('phone')
    .optional()
    .trim(),

  body('website')
    .optional({ checkFalsy: true })
    .isURL()
    .withMessage('Please provide a valid website URL'),

  body('businessType')
    .optional()
    .isIn(['sole_proprietorship', 'partnership', 'corporation', 'llc', 'ngo', 'wellness_center', 'other'])
    .withMessage('Invalid business type'),

  body('servicesOffered')
    .optional()
    .isArray()
    .withMessage('Services offered must be an array'),

  body('priceRange')
    .optional()
    .isIn(['$', '$$', '$$$', '$$$$'])
    .withMessage('Price range must be $, $$, $$$, or $$$$'),

  // Location validation
  body('streetAddress')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Street address must be between 5 and 255 characters'),

  body('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City must be between 2 and 100 characters'),

  body('postalCode')
    .optional({ checkFalsy: true })
    .matches(/^\d{4,10}$/)
    .withMessage('Postal code must be 4-10 digits')
];

/**
 * Validation rules for verification submission
 */
const validateVerificationSubmission = [
  body('instituteId')
    .isInt({ min: 1 })
    .withMessage('Valid institute ID is required'),

  body('verificationType')
    .isIn(['business_registration', 'tax_certificate', 'health_permit', 'professional_license', 'insurance_certificate'])
    .withMessage('Invalid verification type'),

  body('documentType')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Document type must be between 2 and 100 characters'),

  body('documentNumber')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Document number must not exceed 100 characters'),

  body('documentExpiryDate')
    .optional()
    .isISO8601()
    .withMessage('Document expiry date must be a valid date')
];

/**
 * Validation rules for verification review
 */
const validateVerificationReview = [
  body('action')
    .isIn(['approve', 'reject', 'under_review'])
    .withMessage('Action must be approve, reject, or under_review'),

  body('reviewerNotes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Reviewer notes must not exceed 1000 characters'),

  body('rejectionReason')
    .if(body('action').equals('reject'))
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Rejection reason must be between 10 and 500 characters when rejecting')
];

/**
 * Validation rules for geocoding
 */
const validateGeocode = [
  body('address')
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Address must be between 5 and 255 characters'),

  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Country must be between 2 and 100 characters')
];

/**
 * Validation rules for reverse geocoding
 */
const validateReverseGeocode = [
  body('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),

  body('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180')
];

/**
 * Validation rules for address validation
 */
const validateAddressValidation = [
  body('streetAddress')
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Street address must be between 5 and 255 characters'),

  body('city')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City must be between 2 and 100 characters'),

  body('postalCode')
    .optional()
    .matches(/^\d{4,10}$/)
    .withMessage('Postal code must be 4-10 digits'),

  body('country')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Country must be between 2 and 100 characters')
];

/**
 * Validation rules for nearby search
 */
const validateNearbySearch = [
  query('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),

  query('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),

  query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Radius must be between 0.1 and 100 kilometers'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

/**
 * Validation rules for distance calculation
 */
const validateDistanceCalculation = [
  body('lat1')
    .isFloat({ min: -90, max: 90 })
    .withMessage('First latitude must be between -90 and 90'),

  body('lon1')
    .isFloat({ min: -180, max: 180 })
    .withMessage('First longitude must be between -180 and 180'),

  body('lat2')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Second latitude must be between -90 and 90'),

  body('lon2')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Second longitude must be between -180 and 180')
];

module.exports = {
  validateRegistration,
  validateLogin,
  validateProfileUpdate,
  validateInstituteRegistration,
  validateInstituteUpdate,
  validateVerificationSubmission,
  validateVerificationReview,
  validateGeocode,
  validateReverseGeocode,
  validateAddressValidation,
  validateNearbySearch,
  validateDistanceCalculation
};
