const assert = require('assert');

/**
 * Test Suite: Timing Conflict Detection
 * Tests for appointment timing conflicts with daily timings
 */

// Helper function to convert time string to minutes
function timeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

// Helper function to check if appointment time is within operating hours
function isWithinOperatingHours(appointmentTime, operatingStart, operatingEnd) {
  const appointmentMinutes = timeToMinutes(appointmentTime);
  const startMinutes = timeToMinutes(operatingStart);
  const endMinutes = timeToMinutes(operatingEnd);
  
  return appointmentMinutes >= startMinutes && appointmentMinutes < endMinutes;
}

// Helper function to check for time slot overlap
function hasTimeSlotOverlap(slot1Start, slot1Duration, slot2Start, slot2Duration) {
  const slot1End = slot1Start + slot1Duration;
  const slot2End = slot2Start + slot2Duration;
  
  return (slot1Start < slot2End) && (slot1End > slot2Start);
}

console.log('🧪 Running Timing Conflict Detection Tests...\n');

let passedTests = 0;
let failedTests = 0;

const runTest = (testName, testFn) => {
  try {
    console.log(`\n📋 ${testName}`);
    testFn();
    passedTests++;
    console.log(`✅ ${testName} PASSED`);
  } catch (error) {
    failedTests++;
    console.error(`❌ ${testName} FAILED: ${error.message}`);
  }
};

// Test 1: Operating Hours Validation
runTest('Appointment within operating hours (9 AM - 6 PM)', () => {
  const operatingStart = '09:00';
  const operatingEnd = '18:00';
  
  const testCases = [
    { time: '08:00', expected: false, desc: 'Before opening' },
    { time: '09:00', expected: true, desc: 'At opening' },
    { time: '12:00', expected: true, desc: 'Midday' },
    { time: '17:59', expected: true, desc: 'Before closing' },
    { time: '18:00', expected: false, desc: 'At closing (exclusive)' },
    { time: '19:00', expected: false, desc: 'After closing' },
  ];
  
  testCases.forEach(testCase => {
    const result = isWithinOperatingHours(testCase.time, operatingStart, operatingEnd);
    assert.strictEqual(result, testCase.expected, `${testCase.desc} failed`);
    console.log(`  ✓ ${testCase.desc}: ${testCase.time} = ${result}`);
  });
});

// Test 2: Time Slot Overlap Detection
runTest('Time slot overlap detection with service duration', () => {
  // Existing appointment: 14:00 - 15:00 (60 min)
  const existingStart = timeToMinutes('14:00');
  const existingDuration = 60;
  
  const testCases = [
    { time: '13:00', duration: 60, expected: false, desc: 'Before appointment' },
    { time: '13:30', duration: 60, expected: true, desc: 'Overlaps start' },
    { time: '14:00', duration: 60, expected: true, desc: 'Exact same time' },
    { time: '14:30', duration: 30, expected: true, desc: 'Inside appointment' },
    { time: '14:59', duration: 1, expected: true, desc: 'Last minute overlap' },
    { time: '15:00', duration: 60, expected: false, desc: 'After appointment' },
  ];
  
  testCases.forEach(testCase => {
    const slotStart = timeToMinutes(testCase.time);
    const result = hasTimeSlotOverlap(existingStart, existingDuration, slotStart, testCase.duration);
    assert.strictEqual(result, testCase.expected, `${testCase.desc} failed`);
    console.log(`  ✓ ${testCase.desc}: ${testCase.time} (${testCase.duration}min) = ${result}`);
  });
});

// Test 3: Multiple Appointments Conflict Detection
runTest('Multiple appointments conflict detection', () => {
  const appointments = [
    { time: '09:00', duration: 60 },
    { time: '10:00', duration: 60 },
    { time: '14:00', duration: 60 },
  ];
  
  const testCases = [
    { time: '09:30', duration: 30, expected: true, desc: 'Conflicts with first appointment' },
    { time: '11:00', duration: 60, expected: false, desc: 'No conflict' },
    { time: '14:30', duration: 30, expected: true, desc: 'Conflicts with last appointment' },
  ];
  
  testCases.forEach(testCase => {
    const slotStart = timeToMinutes(testCase.time);
    let hasConflict = false;
    
    for (const apt of appointments) {
      const aptStart = timeToMinutes(apt.time);
      if (hasTimeSlotOverlap(aptStart, apt.duration, slotStart, testCase.duration)) {
        hasConflict = true;
        break;
      }
    }
    
    assert.strictEqual(hasConflict, testCase.expected, `${testCase.desc} failed`);
    console.log(`  ✓ ${testCase.desc}: ${testCase.time} = ${hasConflict}`);
  });
});

// Test 4: Service Duration Impact on Conflicts
runTest('Service duration impact on conflict detection', () => {
  const existingStart = timeToMinutes('14:00');
  const existingDuration = 120; // 2-hour service
  
  const testCases = [
    { time: '13:00', duration: 60, expected: false, desc: '1-hour before' },
    { time: '13:30', duration: 60, expected: true, desc: 'Overlaps with 2-hour service' },
    { time: '15:00', duration: 60, expected: true, desc: 'Inside 2-hour service' },
    { time: '15:59', duration: 1, expected: true, desc: 'Last minute of 2-hour service' },
    { time: '16:00', duration: 60, expected: false, desc: 'After 2-hour service' },
  ];
  
  testCases.forEach(testCase => {
    const slotStart = timeToMinutes(testCase.time);
    const result = hasTimeSlotOverlap(existingStart, existingDuration, slotStart, testCase.duration);
    assert.strictEqual(result, testCase.expected, `${testCase.desc} failed`);
    console.log(`  ✓ ${testCase.desc}: ${testCase.time} = ${result}`);
  });
});

// Test 5: Edge Cases
runTest('Edge cases in timing conflicts', () => {
  const existingStart = timeToMinutes('14:00');
  const existingDuration = 60;
  
  const testCases = [
    { time: '13:59', duration: 1, expected: false, desc: 'Ends exactly at start' },
    { time: '14:00', duration: 1, expected: true, desc: 'Starts exactly at start' },
    { time: '14:59', duration: 1, expected: true, desc: 'Starts at last minute' },
    { time: '15:00', duration: 1, expected: false, desc: 'Starts exactly at end' },
  ];
  
  testCases.forEach(testCase => {
    const slotStart = timeToMinutes(testCase.time);
    const result = hasTimeSlotOverlap(existingStart, existingDuration, slotStart, testCase.duration);
    assert.strictEqual(result, testCase.expected, `${testCase.desc} failed`);
    console.log(`  ✓ ${testCase.desc}: ${testCase.time} = ${result}`);
  });
});

console.log(`\n\n📊 Test Results:`);
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`📈 Total: ${passedTests + failedTests}`);

if (failedTests === 0) {
  console.log('\n🎉 All timing conflict tests passed!');
  process.exit(0);
} else {
  console.log('\n⚠️ Some tests failed!');
  process.exit(1);
}

module.exports = {
  timeToMinutes,
  isWithinOperatingHours,
  hasTimeSlotOverlap
};

