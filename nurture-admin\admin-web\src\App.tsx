import { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import { Landing } from './pages/Landing'
import { Login } from './pages/Login'
import { Dashboard } from './pages/Dashboard'
import { Institutes } from './pages/Institutes'
import { InstituteDetail } from './pages/InstituteDetail'
import { Users } from './pages/Users'
import { UserDetail } from './pages/UserDetail'
import { Appointments } from './pages/Appointments'
import { AdminProfile } from './pages/AdminProfile'
import { AdminManagement } from './pages/AdminManagement'
import { Analytics } from './pages/Analytics'
import { PrivacyPolicy } from './pages/PrivacyPolicy'
import { PublicPrivacyPolicy } from './pages/PublicPrivacyPolicy'
import { ErrorLogs } from './pages/ErrorLogs'
import { Layout } from './components/Layout'
import { ProtectedRoute } from './components/ProtectedRoute'
import { LoadingSpinner } from './components/ui/LoadingSpinner'

function App() {
  const { checkAuth, isLoading } = useAuthStore()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-surface">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Landing />} />
        <Route path="/login" element={<Login />} />
        <Route path="/privacy-policy" element={<PublicPrivacyPolicy />} />

        {/* Protected routes */}
        <Route path="/admin" element={<ProtectedRoute><Layout /></ProtectedRoute>}>
          <Route index element={<Navigate to="/admin/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="institutes" element={<Institutes />} />
          <Route path="institutes/:id" element={<InstituteDetail />} />
          <Route path="users" element={<Users />} />
          <Route path="users/:id" element={<UserDetail />} />
          <Route path="appointments" element={<Appointments />} />
          <Route path="analytics" element={<Analytics />} />
          <Route path="admin-management" element={<AdminManagement />} />
          <Route path="profile" element={<AdminProfile />} />
          <Route path="privacy-policy" element={<PrivacyPolicy />} />
          <Route path="error-logs" element={<ErrorLogs />} />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  )
}

export default App
