version: '3.8'

services:
  # PostgreSQL Database
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: nurture-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: ${DB_NAME:-nurture_db}
  #     POSTGRES_USER: ${DB_USER:-postgres}
  #     POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
  #   ports:
  #     - "${DB_PORT:-5432}:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./src/database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
  #   networks:
  #     - nurture-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-nurture_db}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  # Node.js Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nurture-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${PORT:-3000}
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME:-nurture_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-password}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-24h}
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_SECURE: ${EMAIL_SECURE:-false}
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS:-your-app-password}
      EMAIL_FROM_NAME: ${EMAIL_FROM_NAME:-Nurture-Wellness}
      EMAIL_FROM_ADDRESS: ${EMAIL_FROM_ADDRESS:-<EMAIL>}
    ports:
      - "${PORT:-3000}:3000"
    # depends_on:
    #   postgres:
    #     condition: service_healthy
    # networks:
    #   - nurture-network
    volumes:
      # Mount source code for development (comment out for production)
      - .:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin (Optional - for database management)
#   pgadmin:
#     image: dpage/pgadmin4:latest
#     container_name: nurture-pgadmin
#     restart: unless-stopped
#     environment:
#       PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
#       PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
#     ports:
#       - "${PGADMIN_PORT:-8080}:80"
#     depends_on:
#       - postgres
#     networks:
#       - nurture-network
#     profiles:
#       - tools

# volumes:
#   postgres_data:
#     driver: local

# networks:
#   nurture-network:
#     driver: bridge
